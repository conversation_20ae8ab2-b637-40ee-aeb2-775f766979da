(()=>{"use strict";var e,t,s,a,r,o,n,i={523:(e,t,s)=>{s.d(t,{V:()=>i});var a=s(4848),r=s(5284),o=s(6948),n=s(7520);const i=()=>{const{config:e}=(0,o.UK)(),t=e?.persona||"default",s=n.z$[t]||n.z$.default,i=(0,r.cn)("flex","items-center","justify-center","h-full","fixed","w-full","top-[10%]","pointer-events-none"),l=(0,r.cn)("fixed","opacity-[0.03]","z-[1]");return(0,a.jsx)("div",{className:i,children:(0,a.jsx)("img",{src:s,alt:"",className:l,style:{zoom:"1.2"}})})}},1979:(e,t,s)=>{s.d(t,{z:()=>k});var a=s(4848),r=s(6540),o=s(2090),n=s(4539),i=s(6532),l=s(9696),c=s(7086),d=s(6250),u=s(37),m=s(6555),h=s(7197),p=s(5284);function g({...e}){return(0,a.jsx)(h.bL,{"data-slot":"hover-card",...e})}function f({...e}){return(0,a.jsx)(h.l9,{"data-slot":"hover-card-trigger",...e})}function x({className:e,align:t="center",sideOffset:s=4,...r}){return(0,a.jsx)(h.ZL,{"data-slot":"hover-card-portal",children:(0,a.jsx)(h.UC,{"data-slot":"hover-card-content",align:t,sideOffset:s,className:(0,p.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...r})})}var v=s(888),b=s(2955),y=s(6948),w=s(1319),j=s(1905),N=s(6174),C=s(6508);const S={...C.Af,pre:e=>(0,a.jsx)(C.AC,{...e,wrapperClassName:"my-2",className:(0,p.cn)("bg-[var(--code-bg)] text-[var(--code-text)]",e.className),buttonClassName:"h-7 w-7 text-[var(--text)] hover:bg-[var(--text)]/10"})},k=({triggerOpenCreateModal:e,onModalOpened:t})=>{const[s,h]=(0,r.useState)([]),[C,k]=(0,r.useState)(""),[$,M]=(0,r.useState)(1),[A,E]=(0,r.useState)(null),[T,P]=(0,r.useState)(!1),[z,L]=(0,r.useState)(""),[_,R]=(0,r.useState)(""),[O,I]=(0,r.useState)(""),[D,F]=(0,r.useState)(null),{config:U}=(0,y.UK)(),q=(0,r.useCallback)((async()=>{const e=await(0,b.oK)();h(e)}),[]);(0,r.useEffect)((()=>{q()}),[q]);const W=(0,r.useCallback)((e=>{E(null),L(e?.title||""),R(e?.content||""),I(""),P(!0)}),[]);(0,r.useEffect)((()=>{(async()=>{try{const[e]=await chrome.tabs.query({active:!0,currentWindow:!0});e?.id?(console.log(`[NoteSystemView] Component mounted for tab ${e.id}. Sending SIDE_PANEL_READY signal.`),chrome.runtime.sendMessage({type:"SIDE_PANEL_READY",tabId:e.id},(e=>{chrome.runtime.lastError?console.warn("[NoteSystemView] Could not send ready signal:",chrome.runtime.lastError.message):console.log("[NoteSystemView] Background acknowledged ready signal:",e)}))):console.error("[NoteSystemView] Could not determine the tab ID to send ready signal.")}catch(e){console.error("[NoteSystemView] Error sending ready signal:",e)}})()}),[]),(0,r.useEffect)((()=>{const e=(e,t,s)=>{let a=!1;return"CREATE_NOTE_FROM_PAGE_CONTENT"===e.type&&e.payload?(console.log("[NoteSystemView] Received page data. Storing it in state to trigger auto-save."),F(e.payload),s({status:"PAGE_DATA_QUEUED_FOR_AUTO_SAVE"}),a=!0):"ERROR_OCCURRED"===e.type&&e.payload&&(console.log("[NoteSystemView] Received ERROR_OCCURRED via runtime message."),v.oR.error(String(e.payload)),s({status:"ERROR_DISPLAYED_BY_NOTESYSTEM"}),a=!0),!!a};chrome.runtime.onMessage.addListener(e);const t=chrome.runtime.connect({name:N.A.SidePanelPort});return t.postMessage({type:"init"}),t.onMessage.addListener((e=>{if("ADD_SELECTION_TO_NOTE"===e.type){console.log("[NoteSystemView] Handling ADD_SELECTION_TO_NOTE via port");const t=_?`${_}\n\n${e.payload}`:e.payload;T?R(t):W({content:t,title:"Note with Selection"}),v.oR.success("Selection added to note draft.")}})),()=>{console.log("[NoteSystemView] Cleaning up listeners."),chrome.runtime.onMessage.removeListener(e),t.disconnect()}}),[T,_,W]),(0,r.useEffect)((()=>{(async()=>{if(D){console.log("[NoteSystemView] pendingPageData detected. Attempting automatic save.");const e={...D};if(F(null),!e.content||""===e.content.trim())return void v.oR.error("Cannot save note: Content is empty.");const t={title:e.title.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:e.content,tags:[]};try{await(0,b.s2)(t),v.oR.success("Page added to notes!"),await q()}catch(e){console.error("[NoteSystemView] Error auto-saving note:",e),v.oR.error("Failed to auto-save note.")}}})()}),[D,q]),(0,r.useEffect)((()=>{e&&(W(),t())}),[e,t,W]);const B=(0,r.useMemo)((()=>{if(!C)return s;const e=C.toLowerCase();return s.filter((t=>{const s=t.title.toLowerCase().includes(e),a=t.content.toLowerCase().includes(e),r=t.tags&&t.tags.some((t=>t.toLowerCase().includes(e)));return s||a||r}))}),[s,C]),G=(0,r.useMemo)((()=>{const e=12*($-1);return B.slice(e,e+12)}),[B,$]),H=(0,r.useMemo)((()=>Math.max(1,Math.ceil(B.length/12))),[B]);return(0,a.jsxs)("div",{className:"flex flex-col h-full text-[var(--text)]",children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.p,{type:"text",placeholder:"Search notes (titles & content & tags)...",value:C,onChange:e=>k(e.target.value),className:(0,p.cn)("w-full bg-background text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10 border-none rounded-none")}),(0,a.jsx)(d.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(n.F,{className:"flex-1",children:0===G.length?(0,a.jsx)("p",{className:"text-center text-[var(--muted-foreground)] py-4",children:C?`No notes found for "${C}".`:"No notes yet. Create one!"}):(0,a.jsx)("div",{className:"space-y-0",children:G.map((e=>(0,a.jsx)("div",{className:"px-2 border-b border-[var(--text)]/10 rounded-none hover:shadow-lg transition-shadow w-full",children:(0,a.jsxs)(g,{openDelay:200,closeDelay:100,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(f,{asChild:!0,children:(0,a.jsx)("h3",{className:"font-semibold text-md truncate cursor-pointer hover:underline",children:e.title})}),(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)(m.AM,{children:[(0,a.jsx)(m.Wv,{asChild:!0,children:(0,a.jsx)(o.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(u.jbe,{})})}),(0,a.jsxs)(m.hl,{className:"w-30 bg-[var(--popover)] border-[var(--text)]/10 text-[var(--popover-foreground)] mr-1 p-1 space-y-1 shadow-md",children:[(0,a.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>(e=>{const t=e.tags?e.tags.join(", "):"";E(e),L(e.title),R(e.content),I(t),P(!0)})(e),children:[(0,a.jsx)(d.i5t,{className:"mr-2 size-4"}),"Edit"]}),(0,a.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>{let t="---\n";t+=`title: ${e.title}\n`;const s=e.lastUpdatedAt||e.createdAt;if(s){const e=new Date(s).toISOString().split("T")[0];t+=`date: ${e}\n`}e.tags&&e.tags.length>0&&(t+="tags:\n",e.tags.forEach((e=>{t+=`  - ${e.trim()}\n`}))),t+="---\n\n",t+=e.content;const a=document.createElement("a");a.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(t)}`),a.setAttribute("download",`${e.title}.md`),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)},children:[(0,a.jsx)(d.rII,{className:"mr-2 size-4"}),"ObsidianMD"]}),(0,a.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal text-red-500 hover:text-red-500 hover:bg-red-500/10",onClick:()=>(async e=>{await(0,b.VZ)(e),v.oR.success("Note deleted!"),q()})(e.id),children:[(0,a.jsx)(d.ttk,{className:"mr-2 size-4"})," Delete "]})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Last updated: ",new Date(e.lastUpdatedAt).toLocaleDateString()]}),e.tags&&e.tags.length>0?(0,a.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)] truncate max-w-[50%]",children:["Tags: ",e.tags.join(", ")]}):(0,a.jsx)("p",{className:"text-xs text-[var(--muted-foreground)]",children:"No tags"})]}),(0,a.jsx)(x,{className:(0,p.cn)("bg-[var(--popover)] border-[var(--active)] text-[var(--popover-foreground)] markdown-body","w-[80vw] sm:w-[70vw] md:w-[50vw] lg:w-[40vw]","max-w-lg","max-h-[70vh]","overflow-y-auto thin-scrollbar"),side:"top",align:"start",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Date: ",new Date(e.lastUpdatedAt).toLocaleString()]}),(0,a.jsx)("div",{className:"text-sm whitespace-pre-wrap break-words",children:(0,a.jsx)(w.oz,{remarkPlugins:[j.A],components:S,children:e.content})}),e.tags&&e.tags.length>0&&(0,a.jsxs)("div",{className:"border-t border-[var(--border)] pt-2 mt-2",children:[(0,a.jsx)("p",{className:"text-xs font-semibold text-[var(--text)] mb-1",children:"Tags:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e=>(0,a.jsx)("span",{className:"text-xs bg-[var(--muted)] text-[var(--muted-foreground)] px-2 py-0.5 rounded",children:e},e)))})]})]})})]})},e.id)))})}),H>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 font-['Space_Mono',_monospace]",children:[(0,a.jsx)(o.$,{onClick:()=>M((e=>Math.max(1,e-1))),disabled:1===$,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,a.jsxs)("span",{className:"text-md",children:["Page ",$," of ",H]}),(0,a.jsx)(o.$,{onClick:()=>M((e=>Math.min(H,e+1))),disabled:$===H,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]}),(0,a.jsx)(c.lG,{open:T,onOpenChange:e=>{e?P(!0):(P(!1),E(null),L(""),R(""),I(""))},children:(0,a.jsxs)(c.Cf,{className:(0,p.cn)("bg-[var(--bg)] border-[var(--text)]/10 w-[90vw] max-w-3xl text-[var(--text)] overflow-hidden","flex flex-col max-h-[85vh]","p-6"),children:[(0,a.jsxs)(c.c7,{children:[(0,a.jsx)(c.L3,{children:A?"Edit Note":"Create New Note"}),(0,a.jsx)(c.rr,{className:"text-[var(--text)]/80 pt-1",children:A?"Update the title or content of your note.":"Provide a title (optional) and content for your new note."})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-h-0 space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(i.p,{placeholder:"Note Title (optional)",value:z,onChange:e=>L(e.target.value),className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto thin-scrollbar min-h-0",children:(0,a.jsx)(l.T,{placeholder:"Your note content...",value:_,onChange:e=>R(e.target.value),autosize:!0,minRows:5,className:"w-full bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] resize-none overflow-hidden"})}),(0,a.jsx)("div",{children:(0,a.jsx)(i.p,{placeholder:"Tags (comma-separated)",value:O,onChange:e=>{I(e.target.value)},className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})})]}),(0,a.jsxs)(c.Es,{children:[(0,a.jsx)(o.$,{variant:"outline",onClick:()=>{P(!1),E(null)},children:"Cancel"}),(0,a.jsx)(o.$,{onClick:async()=>{if(!_.trim())return void v.oR.error("Note content cannot be empty.");const e=""===O.trim()?[]:O.split(",").map((e=>e.trim())).filter((e=>e.length>0)),t={id:A?.id,title:z.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:_,tags:e};await(0,b.s2)(t),v.oR.success(A?"Note updated!":"Note created!"),await q(),P(!1),E(null),L(""),R(""),I("")},className:"bg-[var(--active)] text-[var(--active-foreground)] hover:bg-[var(--active)]/90",children:A?"Save Changes":"Create Note"})]})]})})]})}},2090:(e,t,s)=>{s.d(t,{$:()=>l});var a=s(4848),r=(s(6540),s(3362)),o=s(2732),n=s(5284);const i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none shrink-0 [&_svg]:shrink-0 outline-none not-focus-visible",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 not-focus-visible",outline:"border bg-background shadow-xs hover:bg-accent",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:text-foreground hover:bg-black/10 dark:hover:bg-white/10",link:"text-primary underline-offset-4 hover:underline","message-action":"bg-transparent text-muted-foreground p-0 shadow-none hover:bg-transparent focus:bg-transparent active:text-muted active:[&_svg]:text-muted-foreground transition-colors duration-75","ghost-themed":"text-foreground hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground","outline-themed":"border border-[var(--active)] bg-transparent text-[var(--active)] shadow-xs hover:bg-[var(--active)]/20 focus-visible:bg-[var(--active)]/20 focus-visible:ring-1 focus-visible:ring-[var(--active)]","destructive-outline":"border border-destructive bg-transparent text-destructive shadow-xs hover:bg-destructive/10 hover:text-destructive-foreground focus-visible:bg-destructive/10 focus-visible:text-destructive-foreground focus-visible:ring-1 focus-visible:ring-destructive","copy-button":"bg-background text-foreground shadow-none hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground",connect:"bg-[var(--input-background)] text-[var(--text)] hover:bg-[var(--active)]/90 shadow-sm focus-visible:ring-1 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]","active-bordered":"bg-[var(--active)] text-[var(--text)] border border-[var(--text)] hover:brightness-110 focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm","outline-subtle":"border border-[var(--text)]/50 bg-transparent text-[var(--text)] hover:bg-[var(--text)]/10 hover:border-[var(--text)]/70 hover:text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm"},size:{default:"h-9 px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",sm:"h-8 rounded-md px-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-4",lg:"h-10 rounded-md px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",icon:"size-8 [&_svg:not([class*='size-'])]:size-7",xs:"h-6 w-6 p-0 rounded-sm [&_svg:not([class*='size-'])]:size-3.5 text-xs"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:o=!1,...l}){const c=o?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:s,className:e})),...l})}},2823:(e,t,s)=>{s.d(t,{w:()=>q});var a=s(4848),r=s(6948),o=s(6540),n=s(888),i=s(3),l=s(2090),c=s(6532),d=s(5284);const u=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),[s,u]=(0,o.useState)(e?.ollamaUrl||"http://localhost:11434"),[m,h]=(0,o.useState)(!1),p=()=>{h(!0),n.Ay.dismiss(),n.Ay.loading("Connecting to Ollama..."),fetch(`${s}/api/tags`).then((e=>e.ok?e.json():e.json().then((t=>{throw new Error(t?.error||`Connection failed: ${e.status} ${e.statusText}`)})).catch((()=>{throw new Error(`Connection failed: ${e.status} ${e.statusText}`)})))).then((a=>{Array.isArray(a.models)?(t({ollamaConnected:!0,ollamaUrl:s,ollamaError:void 0,models:(e?.models||[]).filter((e=>"ollama_generic"!==e.id)).concat([{id:"ollama_generic",host:"ollama",active:!0,name:"Ollama Model"}]),selectedModel:"ollama_generic"}),n.Ay.dismiss(),n.Ay.success("Connected to ollama")):a?.error?(t({ollamaError:a.error,ollamaConnected:!1}),n.Ay.dismiss(),n.Ay.error("string"==typeof a.error?a.error:"Ollama connection error")):(t({ollamaError:"Unexpected response from Ollama",ollamaConnected:!1}),n.Ay.dismiss(),n.Ay.error("Unexpected response from Ollama"))})).catch((e=>{n.Ay.dismiss(),n.Ay.error(e.message||"Failed to connect to Ollama"),t({ollamaError:e.message,ollamaConnected:!1})})).finally((()=>{h(!1)}))},g=e?.ollamaConnected;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c.p,{id:"ollama-url-input",value:s,onChange:e=>u(e.target.value),placeholder:"http://localhost:11434",className:"pr-8",disabled:m}),!g&&(0,a.jsx)(l.$,{onClick:p,variant:"connect",size:"sm",disabled:m,children:m?"...":"Connect"}),g&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm","aria-label":"Connected to Ollama",className:(0,d.cn)("w-8 rounded-md text-[var(--success)]"),disabled:m,onClick:p,children:(0,a.jsx)(i.YrT,{className:"h-5 w-5"})})]})};var m=s(9018),h=s(8698);const p=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),{fetchAllModels:s}=(0,h.N)(),[c,u]=(0,o.useState)(!1),[p,g]=(0,o.useState)(null),f=async()=>{if(e?.ollamaUrl&&e?.ollamaConnected){u(!0),g(null);try{await s(),n.Ay.success("Models refreshed successfully")}catch(e){const t=e instanceof Error?e.message:"Failed to refresh models";g(t),n.Ay.error(t),console.error("Error refreshing models:",e)}finally{u(!1)}}else g("Ollama not connected")};(0,o.useEffect)((()=>{e?.ollamaConnected?f():g(null)}),[e?.ollamaConnected,e?.ollamaUrl]);const x=e?.ollamaConnected,v=e?.selectedModel,b=e?.models?.filter((e=>"ollama"===e.host))||[];return x?p?(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center text-red-500",children:[(0,a.jsx)(i.y3G,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:p})]}),(0,a.jsx)(l.$,{onClick:f,variant:"ghost",size:"sm",disabled:c,className:"h-8 w-8 p-0",children:(0,a.jsx)(i.jTZ,{className:(0,d.cn)("h-4 w-4",c&&"animate-spin")})})]})}):0!==b.length||c?(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)(m.l6,{value:v||"",onValueChange:e=>{t({selectedModel:e}),n.Ay.success(`Selected model: ${e}`)},disabled:c||0===b.length,children:[(0,a.jsx)(m.bq,{variant:"settingsPanel",className:(0,d.cn)("w-full",c&&"opacity-50"),children:(0,a.jsx)(m.yv,{placeholder:c?"Loading models...":"Select a model..."})}),(0,a.jsx)(m.gC,{variant:"settingsPanel",children:b.map((e=>(0,a.jsx)(m.eb,{value:e.id,focusVariant:"activeTheme",className:"text-[var(--text)]",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name||e.id}),e.context_length&&(0,a.jsxs)("span",{className:"text-xs text-[var(--text)]/60",children:["Context: ",e.context_length]})]})},e.id)))})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[v&&b.some((e=>e.id===v))&&(0,a.jsx)("div",{className:"flex items-center text-[var(--success)]",children:(0,a.jsx)(i.YrT,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{onClick:f,variant:"ghost",size:"sm",disabled:c,className:"h-8 w-8 p-0 hover:bg-[var(--text)]/10",title:"Refresh models",children:(0,a.jsx)(i.jTZ,{className:(0,d.cn)("h-4 w-4",c&&"animate-spin")})})]})]}):(0,a.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,a.jsxs)("div",{className:"flex items-center text-[var(--text)]/60",children:[(0,a.jsx)(i.y3G,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:"No models available"})]}),(0,a.jsx)(l.$,{onClick:f,variant:"ghost",size:"sm",disabled:c,className:"h-8 w-8 p-0",children:(0,a.jsx)(i.jTZ,{className:(0,d.cn)("h-4 w-4",c&&"animate-spin")})})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center py-4 text-[var(--text)]/60",children:[(0,a.jsx)(i.y3G,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Connect to Ollama first"})]})},g=({title:e,Component:t})=>(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-border/50 last:border-b-0",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,a.jsx)("h4",{className:"text-apple-body font-semibold text-foreground",children:e})}),(0,a.jsx)(t,{})]}),f=()=>(0,a.jsxs)("div",{className:(0,d.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,a.jsx)("div",{className:(0,d.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"API Access"}),(0,a.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Connect to your AI services"})]})}),(0,a.jsxs)("div",{className:"divide-y divide-border/50",children:[(0,a.jsx)(g,{Component:u,title:"Ollama"}),(0,a.jsx)(g,{Component:p,title:"Model Selection"})]})]});var x=s(803),v=s(2732);const b=(0,v.F)("relative flex w-full touch-none select-none items-center data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",{variants:{variant:{default:["[&>span[data-slot=slider-track]]:bg-secondary","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-primary","[&>button[data-slot=slider-thumb]]:bg-background","[&>button[data-slot=slider-thumb]]:border-primary","[&>button[data-slot=slider-thumb]]:ring-offset-background","[&>button[data-slot=slider-thumb]]:focus-visible:ring-ring"],themed:["[&>span[data-slot=slider-track]]:bg-[var(--text)]/10","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:border-[var(--text)]/50","[&>button[data-slot=slider-thumb]]:ring-offset-[var(--bg)]","[&>button[data-slot=slider-thumb]]:focus-visible:ring-[var(--active)]"]}},defaultVariants:{variant:"default"}});function y({className:e,variant:t,defaultValue:s,value:r,min:n=0,max:i=100,...l}){const c=o.useMemo((()=>Array.isArray(r)?r:Array.isArray(s)?s:[n,i]),[r,s,n]);return(0,a.jsxs)(x.bL,{"data-slot":"slider",defaultValue:s,value:r,min:n,max:i,className:(0,d.cn)(b({variant:t,className:e})),...l,children:[(0,a.jsx)(x.CC,{"data-slot":"slider-track",className:(0,d.cn)("relative h-1.5 w-full grow overflow-hidden rounded-full","data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,a.jsx)(x.Q6,{"data-slot":"slider-range",className:(0,d.cn)("absolute h-full","data-[orientation=vertical]:w-full")})}),(c.length>0?c:[n]).map(((e,t)=>(0,a.jsx)(x.zi,{"data-slot":"slider-thumb",className:(0,d.cn)("block h-4 w-4 bg-white rounded-full border border-primary/50 shadow-sm transition-colors focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50")},t)))]})}var w=s(5634);const j=({size:e,updateConfig:t})=>(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"contextLimit",className:"text-apple-body font-medium text-foreground",children:"Character Limit"}),(0,a.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:128===e?"Unlimited":`${e}k chars`})]}),(0,a.jsx)(y,{id:"contextLimit",value:[e],max:128,min:1,step:1,onValueChange:e=>t({contextLimit:e[0]}),className:"w-full"}),(0,a.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum amount of page content to include in context. Higher values provide more context but use more tokens."})]}),N=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),s=e?.contextLimit||1;return(0,a.jsxs)("div",{className:(0,d.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,a.jsx)("div",{className:(0,d.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Page Context"}),(0,a.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Control how much page content to analyze"})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)(j,{size:s,updateConfig:t})})]})},C=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),s=e=>s=>{const a=Array.isArray(s)?s[0]:s;t({[e]:a})},o=e.temperature??.7,n=e.maxTokens??32048,i=e.topP??.95,l=e.presencepenalty??0;return(0,a.jsxs)("div",{className:(0,d.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,a.jsx)("div",{className:(0,d.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Model Parameters"}),(0,a.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Fine-tune AI model behavior"})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"temperature",className:"text-apple-body font-medium text-foreground",children:"Temperature"}),(0,a.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:o.toFixed(2)})]}),(0,a.jsx)(y,{id:"temperature",min:0,max:2,step:.01,value:[o],onValueChange:s("temperature"),className:"w-full"}),(0,a.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Controls randomness. Lower values make responses more focused and deterministic."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"maxTokens",className:"text-apple-body font-medium text-foreground",children:"Max Tokens"}),(0,a.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:n.toLocaleString()})]}),(0,a.jsx)(c.p,{id:"maxTokens",type:"number",value:n,onChange:e=>s("maxTokens")(parseInt(e.target.value)||0),className:"w-full",min:1,max:1e5}),(0,a.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum length of the response in tokens."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"topP",className:"text-apple-body font-medium text-foreground",children:"Top P"}),(0,a.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:i.toFixed(2)})]}),(0,a.jsx)(y,{id:"topP",min:0,max:1,step:.01,value:[i],onValueChange:s("topP"),className:"w-full"}),(0,a.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Controls diversity via nucleus sampling. Lower values focus on more likely tokens."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"presencePenalty",className:"text-apple-body font-medium text-foreground",children:"Presence Penalty"}),(0,a.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:l.toFixed(2)})]}),(0,a.jsx)(y,{id:"presencePenalty",min:-2,max:2,step:.01,value:[l],onValueChange:s("presencepenalty"),className:"w-full"}),(0,a.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Reduces repetition by penalizing tokens that have already appeared."})]})]})]})};var S=s(7086),k=s(3732),$=s(9696);const M=({hasChange:e,onSave:t,onSaveAs:s,onCancel:r})=>e?(0,a.jsxs)("div",{className:"flex mt-4 space-x-2 justify-end w-full",children:[(0,a.jsx)(l.$,{variant:"default",size:"sm",onClick:t,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:"Save"}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:s,children:"Save As..."}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:r,children:"Cancel"})]}):null,A=({isOpen:e,onOpenChange:t,personaPrompt:s,personas:r,updateConfig:n,onModalClose:i})=>{const[d,u]=(0,o.useState)("");return(0,a.jsx)(S.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(S.Cf,{className:"sm:max-w-[425px]",onCloseAutoFocus:e=>e.preventDefault(),children:[(0,a.jsx)(S.c7,{children:(0,a.jsx)(S.L3,{children:"Create New Persona"})}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsx)(w.J,{htmlFor:"persona-name",className:"text-base font-medium text-foreground sr-only",children:"Persona Name"}),(0,a.jsx)(c.p,{id:"persona-name",placeholder:"Enter persona name",value:d,onChange:e=>u(e.target.value)})]}),(0,a.jsxs)(S.Es,{className:"sm:justify-end",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:i,children:"Cancel"}),(0,a.jsx)(l.$,{type:"button",variant:"default",size:"sm",disabled:!d.trim(),onClick:()=>{d.trim()&&(n({personas:{...r,[d.trim()]:s},persona:d.trim()}),u(""),i())},children:"Create"})]})]})})},E=({isOpen:e,onOpenChange:t,persona:s,personas:r,updateConfig:o,onModalClose:n})=>(0,a.jsx)(S.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(S.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(S.c7,{children:[(0,a.jsxs)(S.L3,{children:['Delete "',s,'"']}),(0,a.jsx)(S.rr,{className:"pt-2",children:"Are you sure you want to delete this persona? This action cannot be undone."})]}),(0,a.jsxs)(S.Es,{className:"sm:justify-end pt-4",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:n,children:"Cancel"}),(0,a.jsx)(l.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{const e={...r};delete e[s];const t=Object.keys(e);o({personas:e,persona:t.length>0?t[0]:"Scholar"}),n()},children:"Delete"})]})]})}),T=({personas:e,persona:t,updateConfig:s})=>(0,a.jsxs)(m.l6,{value:t,onValueChange:e=>s({persona:e}),children:[(0,a.jsx)(m.bq,{className:"flex w-full",children:(0,a.jsx)(m.yv,{placeholder:"Select persona"})}),(0,a.jsx)(m.gC,{children:Object.keys(e).map((e=>(0,a.jsx)(m.eb,{value:e,children:e},e)))})]}),P=({personaPrompt:e,setPersonaPrompt:t,isEditing:s,setIsEditing:r})=>{const o={onFocus:e=>{s||r(!0)}};return(0,a.jsx)($.T,{value:e,onChange:e=>{s||r(!0),t(e.target.value)},readOnly:!s,...o,placeholder:"Define the persona's characteristics and instructions here...",className:(0,d.cn)("w-full min-h-[120px] resize-none",s?"hover:border-primary focus:border-primary":"opacity-75 cursor-default"),rows:5})},z=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),[s,n]=(0,o.useState)(!1),[i,c]=(0,o.useState)(!1),[u,m]=(0,o.useState)(!1),h=e?.personas||{Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis."},p=e?.persona||"Scholar",g=h?.[p]??h?.Scholar??"You are The Scholar, an analytical academic researcher specializing in web search analysis.",[f,x]=(0,o.useState)(g),v=u&&f!==g;return(0,o.useEffect)((()=>{x(h?.[p]??h?.Scholar??""),m(!1)}),[p,JSON.stringify(h)]),(0,a.jsxs)("div",{className:(0,d.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,a.jsx)("div",{className:(0,d.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Persona"}),(0,a.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Customize AI personality and behavior"})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(T,{persona:p,personas:h,updateConfig:t})}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm","aria-label":"Add new persona",className:"p-2 h-10 w-10",onClick:()=>{x(""),m(!0),n(!0)},children:(0,a.jsx)(k.YHj,{className:"h-5 w-5"})}),Object.keys(h).length>1&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm","aria-label":"Delete current persona",className:"p-2 h-10 w-10 hover:text-destructive hover:bg-destructive/10",onClick:()=>c(!0),children:(0,a.jsx)(k.dW_,{className:"h-5 w-5"})})]}),(0,a.jsx)(P,{personaPrompt:f,setPersonaPrompt:x,isEditing:u,setIsEditing:m}),(0,a.jsx)(M,{hasChange:v,onSave:()=>{t({personas:{...h,[p]:f}}),m(!1)},onSaveAs:()=>{n(!0)},onCancel:()=>{x(g),m(!1)}})]}),(0,a.jsx)(A,{isOpen:s,onOpenChange:e=>{n(e),e||(x(g),m(!1))},personaPrompt:f,personas:h,updateConfig:t,onModalClose:()=>n(!1)}),(0,a.jsx)(E,{isOpen:i,onOpenChange:c,persona:p,personas:h,updateConfig:t,onModalClose:()=>c(!1)})]})};var L=s(9451),_=s(8309);function R({className:e,...t}){return(0,a.jsx)(L.bL,{"data-slot":"radio-group",className:(0,d.cn)("grid gap-3",e),...t})}const O=(0,v.F)("aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:["border-input dark:bg-input/30 text-primary focus-visible:border-ring focus-visible:ring-ring/50"],themed:["border-[var(--text)] text-[var(--active)]","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-0 focus-visible:border-[var(--active)]","data-[state=checked]:border-[var(--active)]"]}},defaultVariants:{variant:"default"}});function I({className:e,variant:t,...s}){return(0,a.jsx)(L.q7,{"data-slot":"radio-group-item",className:(0,d.cn)(O({variant:t,className:e})),...s,children:(0,a.jsx)(L.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,a.jsx)(_.A,{className:(0,d.cn)("absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2","themed"===t?"fill-[var(--active)]":"fill-primary")})})})}const D=({webMode:e,updateConfig:t})=>(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(w.J,{className:"text-apple-body font-medium text-foreground",children:"Search Provider"}),(0,a.jsx)(R,{value:e,onValueChange:e=>t({webMode:e}),className:"space-y-2",children:["Google"].map((e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(I,{value:e,id:`webMode-${e}`}),(0,a.jsx)(w.J,{htmlFor:`webMode-${e}`,className:"text-apple-body font-medium cursor-pointer",children:e})]},e)))})]}),F=({config:e,updateConfig:t})=>{const s=e?.webLimit??16,r=e?.serpMaxLinksToVisit??3;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"maxLinks",className:"text-apple-body font-medium text-foreground",children:"Max Links to Visit"}),(0,a.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:r})]}),(0,a.jsx)(y,{id:"maxLinks",value:[r],max:10,min:1,step:1,onValueChange:e=>t({serpMaxLinksToVisit:e[0]}),className:"w-full"}),(0,a.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Number of search result links to fetch and analyze."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"charLimit",className:"text-apple-body font-medium text-foreground",children:"Content Character Limit"}),(0,a.jsx)("span",{className:"text-apple-footnote text-muted-foreground font-mono",children:128===s?"Unlimited":`${s}k chars`})]}),(0,a.jsx)(y,{id:"charLimit",value:[s],max:128,min:1,step:1,onValueChange:e=>t({webLimit:e[0]}),className:"w-full"}),(0,a.jsx)("p",{className:"text-apple-caption2 text-muted-foreground",children:"Maximum characters (in thousands) of content to analyze per page. Set to 128k for unlimited."})]})]})},U=()=>{const{config:e,updateConfig:t}=(0,r.UK)();return(0,o.useEffect)((()=>{if("Google"===e?.webMode){const s={};void 0===e?.serpMaxLinksToVisit&&(s.serpMaxLinksToVisit=3),void 0===e?.webLimit&&(s.webLimit=16),Object.keys(s).length>0&&t(s)}}),[e?.webMode,e?.serpMaxLinksToVisit,e?.webLimit,t]),(0,a.jsxs)("div",{className:(0,d.cn)("settings-card","bg-card border border-border rounded-xl shadow-sm","hover:shadow-md hover:border-border/80","overflow-hidden"),children:[(0,a.jsx)("div",{className:(0,d.cn)("settings-card-header","px-6 py-4 border-b border-border/50"),children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-apple-title3 font-semibold text-foreground",children:"Web Search"}),(0,a.jsx)("p",{className:"text-apple-footnote text-muted-foreground",children:"Configure search behavior and limits"})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsx)(D,{updateConfig:t,webMode:e?.webMode}),"Google"===e?.webMode&&(0,a.jsx)("div",{className:"pt-2 border-t border-border/50",children:(0,a.jsx)(F,{config:e,updateConfig:t})}),!e?.webMode&&(0,a.jsx)("div",{className:"pt-2 border-t border-border/50",children:(0,a.jsx)("p",{className:"text-muted-foreground text-apple-body",children:"Select a search provider to configure its settings."})})]})]})},q=()=>{const{config:e}=(0,r.UK)(),[t,s]=(0,o.useState)(!e?.models||0===e.models.length);return(0,a.jsxs)("div",{id:"settings",className:"relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden",children:[(0,a.jsx)("div",{className:"flex justify-center mb-6",children:(0,a.jsx)("div",{id:"kofi-widget",children:(0,a.jsx)("a",{href:"https://ko-fi.com/T6T11G2CYS",target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)("img",{height:"36",style:{border:"0px",height:"36px"},src:"https://storage.ko-fi.com/cdn/kofi6.png?v=6",border:"0",alt:"Buy Me a Coffee at ko-fi.com"})})})}),t&&(0,a.jsx)("div",{className:(0,d.cn)("mb-8 p-6","rounded-xl","bg-card border border-border shadow-sm","text-foreground"),children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("h2",{className:"text-apple-title3 font-semibold text-center",children:"Quick Setup Guide"}),(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"1"}),(0,a.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Fill your API key or URLs in API Access"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"2"}),(0,a.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Exit settings, then use the persona selector to choose your model and start chatting"})]}),(0,a.jsx)("div",{className:"text-apple-footnote text-muted-foreground mt-2 ml-9 italic",children:"Note: You can change other settings now or later. Have fun!"})]}),(0,a.jsx)(l.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>{s(!1)},children:"Get Started"})]})}),(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,a.jsx)(f,{}),(0,a.jsx)(C,{}),(0,a.jsx)(z,{}),(0,a.jsx)(N,{}),(0,a.jsx)(U,{}),(0,a.jsx)("div",{className:"pointer-events-none h-8"})," "]})]})}},2951:(e,t,s)=>{s.d(t,{hL:()=>i,GW:()=>a,hj:()=>l,tE:()=>n});const a=async(e,t,s,a,r,o=[],n)=>{try{if(!s?.host)return console.error("processQueryWithAI: currentModel or currentModel.host is undefined. Cannot determine API URL."),e;const i=o.map((e=>`{{${e.role}}}: ${e.content}`)).join("\n"),l=`You are a Google search query optimizer. Your task is to rewrite user's input [The user's raw input && chat history:${i}].\n\n\nInstructions:\n**Important** No Explanation, just the optimized query!\n\n\n1. Extract the key keywords and named entities from the user's input.\n2. Correct any obvious spelling errors.\n3. Remove unnecessary words (stop words) unless they are essential for the query's meaning.\n4. If the input is nonsensical or not a query, return the original input.\n5. Using previous chat history to understand the user's intent.\n\n\nOutput:\n'The optimized Google search query'\n\n\nExample 1:\nInput from user ({{user}}): where can i find cheep flights to london\nOutput:\n'cheap flights London'\n\n\nExample 2:\nContext: {{user}}:today is a nice day in paris i want to have a walk and find a restaurant to have a nice meal. {{assistant}}: Bonjour, it's a nice day!\nInput from user ({{user}}): please choose me the best restarant\nOutput:\n'best restaurants Paris France'\n\n\nExample 3:\nInput from user ({{user}}): asdf;lkjasdf\nOutput:\n'asdf;lkjasdf'\n`,c={ollama:`${t?.ollamaUrl||""}/api/chat`}[s.host];if(!c)return console.error("processQueryWithAI: Could not determine API URL for host:",s.host),e;console.log(`processQueryWithAI: Using API URL: ${c} for host: ${s.host}`),console.log("Formatted Context for Prompt:",i);const d={model:t?.selectedModel||s.id||"",messages:[{role:"system",content:l},{role:"user",content:e}],stream:!1};let u;void 0!==n?u=n:void 0!==t.temperature&&(u=t.temperature),void 0!==u&&(d.temperature=u);const m=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json",...a||{}},signal:r,body:JSON.stringify(d)});if(!m.ok){const e=await m.text();throw console.error(`API request failed with status ${m.status}: ${e}`),new Error(`API request failed: ${m.statusText}`)}const h=await m.json(),p=h?.choices?.[0]?.message?.content;return"string"==typeof p?(e=>e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/["']/g,"").trim())(p):e}catch(t){if(r?.aborted||t instanceof Error&&"AbortError"===t.name)throw console.log("processQueryWithAI: Operation aborted."),t;return console.error("processQueryWithAI: Error during execution:",t),e}},r=async function(e){try{const t=new URL(e);if("chrome:"===t.protocol)return;const s=[{id:1,priority:1,condition:{requestDomains:[t.hostname]},action:{type:"modifyHeaders",requestHeaders:[{header:"Origin",operation:"set",value:`${t.protocol}//${t.hostname}`}]}}];await chrome.declarativeNetRequest.updateDynamicRules({removeRuleIds:s.map((e=>e.id)),addRules:s})}catch(e){console.debug("URL rewrite skipped:",e)}},o=e=>{try{const t=(new DOMParser).parseFromString(e,"text/html");t.querySelectorAll('script, style, nav, footer, header, svg, img, noscript, iframe, form, aside, .sidebar, .ad, .advertisement, .banner, .popup, .modal, .cookie-banner, link[rel="stylesheet"], button, input, select, textarea, [role="navigation"], [role="banner"], [role="contentinfo"], [aria-hidden="true"]').forEach((e=>e.remove()));let s=t.querySelector("main")||t.querySelector("article")||t.querySelector(".content")||t.querySelector("#content")||t.querySelector(".main-content")||t.querySelector("#main-content")||t.querySelector(".post-content")||t.body,a=s?.textContent||"";return a=a.replace(/\s+/g," ").trim(),a=a.split("\n").filter((e=>e.trim().length>20)).join("\n"),a}catch(e){return console.error("Error parsing HTML for content extraction:",e),"[Error extracting content]"}},n=async(e,t,s)=>{console.log("[webSearch] Received query:",e),console.log("[webSearch] Web Mode from config:",t?.webMode);const a=t.webMode,r=t.serpMaxLinksToVisit??3,n=t.webLimit&&128!==t.webLimit?1e3*t.webLimit:1/0,i=s||(new AbortController).signal;if(console.log(`Performing ${a} search for: "${e}"`),"Google"===a&&console.log(`[webSearch - ${a}] Max links to visit for content scraping: ${r}`),!a)return console.error("[webSearch] Web search mode is undefined. Aborting search. Config was:",JSON.stringify(t)),"Error: Web search mode is undefined. Please check your configuration.";try{if("Google"===a){const t=new AbortController,s=setTimeout((()=>{console.warn(`[webSearch - ${a}] SERP API call timed out after 15s.`),t.abort()}),15e3),l="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i,c=`https://www.google.com/search?q=${encodeURIComponent(e)}&hl=en&gl=us&num=10&start=0&safe=off&filter=0`;console.log(`[webSearch - ${a}] Fetching SERP from: ${c}`);const d=await fetch(c,{signal:l,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"none","Sec-Fetch-User":"?1","Cache-Control":"max-age=0",Referer:"https://www.google.com/"}}).finally((()=>{clearTimeout(s)}));if(console.log(`[webSearch - ${a}] Response status: ${d.status} ${d.statusText}`),console.log(`[webSearch - ${a}] Response headers:`,Object.fromEntries(d.headers.entries())),!d.ok){const e=await d.text();throw console.error(`[webSearch - ${a}] Error response body:`,e.substring(0,1e3)),429===d.status?new Error("Google search rate limited (429). Please try again later."):403===d.status?new Error("Google search access forbidden (403). Google may be blocking automated requests."):d.status>=400&&d.status<500?new Error(`Google search client error (${d.status}): ${d.statusText}`):new Error(`Google search server error (${d.status}): ${d.statusText}`)}if(i.aborted)throw new Error("Web search operation aborted.");const u=await d.text();if(console.log(`[webSearch - ${a}] SERP HTML length: ${u.length} characters`),console.log(`[webSearch - ${a}] SERP HTML (first 500 chars):`,u.substring(0,500)),u.includes("captcha")||u.includes("unusual traffic")||u.includes("blocked"))return console.warn(`[webSearch - ${a}] Detected potential CAPTCHA or blocking page`),"Error: Google search may be blocked. The page contains CAPTCHA or blocking content. Please try again later.";const m=(new DOMParser).parseFromString(u,"text/html"),h=[],p=["div.g","div.MjjYud","div.hlcw0c","div.kvH3mc","div.tF2Cxc","div.yuRUbf"];let g=[];for(const e of p){const t=Array.from(m.querySelectorAll(e));if(t.length>0){g=t,console.log(`[webSearch - ${a}] Found ${t.length} results using selector: ${e}`);break}}if(0===g.length&&(g=Array.from(m.querySelectorAll("div[data-ved], div[data-hveid]")),console.log(`[webSearch - ${a}] Fallback: Found ${g.length} results using data attributes`)),g.forEach(((e,t)=>{try{let s=e.querySelector('a[href^="http"]')||e.querySelector('a[href^="/url?q="]')||e.querySelector("a[href]"),r=s?.getAttribute("href");if(r&&r.startsWith("/url?q=")){const e=new URLSearchParams(r.substring(6));r=e.get("q")||r}const o=e.querySelector("h3")||e.querySelector("h2")||e.querySelector('[role="heading"]')||e.querySelector("a[href] > div"),n=o?.textContent?.trim()||"";let i="";const l=['div[style*="-webkit-line-clamp"]','div[data-sncf="1"]',".VwiC3b span",".MUxGbd span",".s3v9rd",".st",'span[style*="-webkit-line-clamp"]'];for(const t of l){const s=e.querySelectorAll(t);if(s.length>0){i=Array.from(s).map((e=>e.textContent)).join(" ").replace(/\s+/g," ").trim();break}}if(!i&&n){const t=e.textContent||"",s=t.indexOf(n);-1!==s&&(i=t.substring(s+n.length).replace(/\s+/g," ").trim().substring(0,300))}n&&r&&r.startsWith("http")&&!r.includes("google.com/search")&&(h.push({title:n,snippet:i,url:r}),console.log(`[webSearch - ${a}] Parsed result ${t+1}: ${n.substring(0,50)}...`))}catch(e){console.warn(`[webSearch - ${a}] Error parsing result ${t+1}:`,e)}})),console.log(`[webSearch - ${a}] Parsed SERP Results (${h.length} found, showing first 5):`,JSON.stringify(h.slice(0,5))),0===h.length){console.warn(`[webSearch - ${a}] No search results found on SERP.`),console.log(`[webSearch - ${a}] HTML document title:`,m.title),console.log(`[webSearch - ${a}] Available div elements:`,Array.from(m.querySelectorAll("div")).slice(0,10).map((e=>({className:e.className,id:e.id,textContent:e.textContent?.substring(0,100)}))));const e=m.body?.textContent?.toLowerCase()||"";return e.includes("captcha")||e.includes("unusual traffic")||e.includes("blocked")||e.includes("robot")?"Error: Google search appears to be blocked. The page contains CAPTCHA or blocking content. Please try again later or check if the extension has proper permissions.":"No search results found. Google may have changed their page structure or the search was unsuccessful."}const f=h.slice(0,r).filter((e=>e.url));console.log(`Found ${h.length} results. Attempting to fetch content from top ${f.length} links (maxLinksToVisit: ${r}).`);const x=f.map((async e=>{if(!e.url)return{...e,content:"[Invalid URL]",status:"error"};if(i.aborted)return{...e,content:`[Fetching aborted by user: ${e.url}]`,status:"aborted"};console.log(`Fetching content from: ${e.url}`);const t=new AbortController,s=setTimeout((()=>{console.warn(`[webSearch] Page scrape for ${e.url} timed out after 12s.`),t.abort()}),12e3),r="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i;let n=`[Error fetching/processing: Unknown error for ${e.url}]`,l="error";try{const t=await fetch(e.url,{signal:r,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(!t.ok)throw new Error(`Failed to fetch ${e.url} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e.url}`);if(i.aborted)throw new Error("Web search operation aborted by user.");const c=await t.text();n=o(c),l="success",console.log(`[webSearch - ${a}] Successfully fetched and extracted content from: ${e.url} (Extracted Length: ${n.length})`)}catch(s){if("AbortError"===s.name){if(i.aborted)throw s;n=t.signal.aborted?`[Timeout fetching: ${e.url}]`:`[Fetching aborted: ${e.url}]`,l="aborted"}else n=`[Error fetching/processing: ${s.message}]`,l="error"}finally{clearTimeout(s)}return{...e,content:n,status:l}})),v=await Promise.allSettled(x);if(i.aborted)throw new Error("Web search operation aborted.");let b=`Search results for "${e}" using ${a}:\n\n`,y=0;return h.forEach(((e,t)=>{if(b+=`[Result ${t+1}: ${e.title}]\n`,b+=`URL: ${e.url||"[No URL Found]"}\n`,b+=`Snippet: ${e.snippet||"[No Snippet]"}\n`,t<f.length){const t=v[y];if("fulfilled"===t?.status){const s=t.value;if(s.url===e.url){const e=s.content.substring(0,n);b+=`Content:\n${e}${s.content.length>n?"...":""}\n\n`}else b+=`Content: [Content fetch mismatch - data for ${s.url} found, expected ${e.url}]\n\n`}else b+="rejected"===t?.status?`Content: [Error fetching: ${t.reason}]\n\n`:"Content: [Fetch status unknown]\n\n";y++}else b+="Content: [Not fetched due to link limit]\n\n"})),console.log("Web search finished. Returning combined results."),b.trim()}return`Unsupported web search mode: ${a}`}catch(e){if("AbortError"===e.name&&i.aborted)throw console.log("[webSearch] Operation aborted by signal."),e;return console.error("Web search overall failed:",e),`Error performing web search: ${e.message}`}};async function i(e,t,s,a={},o,n){let i=!1;const l=(e,t=!1)=>{if(!i){let a;i=!0,a="string"==typeof e?e:e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:String(e),s(a,!0,t)}},c=()=>{if(n?.aborted)throw new Error("Streaming operation aborted by user.")};if(e.startsWith("chrome://"))console.log("fetchDataAsStream: Skipping chrome:// URL:",e);else{e.includes("localhost")&&await r((e=>e.endsWith("/")?e.slice(0,-1):e)(e));try{const r=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",...a},body:JSON.stringify(t),signal:n});if(!r.ok){let e=`Network response was not ok (${r.status})`;try{e+=`: ${await r.text()||r.statusText}`}catch(t){e+=`: ${r.statusText}`}throw new Error(e)}let d="";if("ollama"!==o)throw new Error(`Unsupported host specified: ${o}`);{if(!r.body)throw new Error("Response body is null for Ollama");const e=r.body.getReader();let t,a;for(;c(),({value:a,done:t}=await e.read()),!t;){const t=(new TextDecoder).decode(a).split("\n").filter((e=>""!==e.trim()));for(const a of t){if("[DONE]"===a.trim())return n?.aborted&&e.cancel(),void l(d);try{const t=JSON.parse(a);if(t.message?.content&&(d+=t.message.content,i||s(d)),!0===t.done&&!i)return n?.aborted&&e.cancel(),void l(d)}catch(e){console.debug("Skipping invalid JSON chunk:",a)}}}n?.aborted&&e.cancel(),l(d)}}catch(e){n?.aborted?(console.log("[fetchDataAsStream] Operation aborted via signal as expected. Details:",e),l("",!1)):e instanceof Error&&"AbortError"===e.name?(console.log("[fetchDataAsStream] AbortError (name check) caught. Operation was cancelled. Details:",e),l("",!1)):(console.error("Error in fetchDataAsStream (unexpected):",e),l(e instanceof Error?e.message:String(e),!0))}}}async function l(e,t){const s=new AbortController,a=t||s.signal,r=t?null:setTimeout((()=>s.abort()),12e3);try{const t=await fetch(e,{signal:a,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(r&&clearTimeout(r),a.aborted)throw new Error("Scraping aborted by user.");if(!t.ok)throw new Error(`Failed to fetch ${e} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e}`);const n=await t.text();return o(n)}catch(t){return r&&clearTimeout(r),"AbortError"===t.name?`[Scraping URL aborted: ${e}]`:`[Error scraping URL: ${e} - ${t.message}]`}}"undefined"!=typeof window&&(window.testGoogleSearchDebug=async function(e="test search"){console.log("🔍 Testing Google search functionality...");const t={webMode:"Google",serpMaxLinksToVisit:2,webLimit:16,personas:{},persona:"Scholar",contextLimit:60,temperature:.7,maxTokens:32480,topP:.95,presencepenalty:0,models:[],chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",useNote:!1,noteContent:"",userName:"user",userProfile:"",popoverTitleDraft:"",popoverTagsDraft:""};try{const s=await n(e,t);return console.log("✅ Search completed successfully!"),console.log("📄 Result length:",s.length),console.log("📋 Result preview:",s.substring(0,500)+"..."),s}catch(e){throw console.error("❌ Search failed:",e),e}})},2955:(e,t,s)=>{s.d(t,{VZ:()=>l,oK:()=>i,s2:()=>n});var a=s(3790),r=s.n(a);const o="cognito_note_",n=async e=>{const t=Date.now(),s=e.id||`${o}${Date.now()}_${Math.random().toString(16).slice(2)}`,a=e.id?await r().getItem(s):null,n={id:s,title:e.title||`Note - ${new Date(t).toLocaleDateString([],{year:"numeric",month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit"})}`,content:e.content,createdAt:a?.createdAt||t,lastUpdatedAt:t,tags:e.tags};return await r().setItem(s,n),n},i=async()=>{const e=(await r().keys()).filter((e=>e.startsWith(o))),t=[];for(const s of e){const e=await r().getItem(s);e&&t.push(e)}return t.sort(((e,t)=>t.lastUpdatedAt-e.lastUpdatedAt))},l=async e=>{await r().removeItem(e),console.log("Note deleted from system:",e)}},3003:(e,t,s)=>{s.a(e,(async(e,t)=>{try{var a=s(4848),r=s(5338),o=s(1468),n=s(3190),i=s(6174),l=s(9828),c=s(6948),d=e([l]);l=(d.then?(await d)():d)[0];const u=(0,n.g)(i.A.ContentPort),m=document.getElementById("root");u.ready().then((()=>{if(null==m)throw new Error("Root container not found");(0,r.createRoot)(m).render((0,a.jsx)(o.Kq,{store:u,children:(0,a.jsx)(c.sG,{children:(0,a.jsx)(l.A,{})})}))})),t()}catch(e){t(e)}}))},3190:(e,t,s)=>{s.d(t,{g:()=>d});var a=s(38),r=s(9448),o=s(7346),n=s(3207),i=s(5886);const l={...s(6108).z2,...i.z2},c=((0,n.nK)(l),o.P,(0,a.N0)(),r.logger,[(0,n.nK)(l),o.P,(0,a.N0)(),r.logger]);(0,n.nK)(l),r.logger;const d=e=>{const t=new n.il({channelName:e});return(0,n.Tw)(t,...c),t}},3828:(e,t,s)=>{s.d(t,{p:()=>w});var a=s(4848),r=s(6540),o=s(6973),n=s(6948),i=s(2090),l=s(3885),c=s(9696),d=s(5284),u=s(6555),m=s(9014);function h({className:e,...t}){return(0,a.jsx)(m.bL,{"data-slot":"switch",className:(0,d.cn)("peer relative inline-flex h-[10px] w-[26px] shrink-0 cursor-pointer items-center rounded-full bg-input transition-colors duration-200 data-[state=checked]:bg-primary data-[state=unchecked]:bg-foreground/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(m.zi,{"data-slot":"switch-thumb",className:(0,d.cn)("pointer-events-none block size-4 rounded-full shadow-md ring-1 transition-transform duration-200 ease-in-out transform","data-[state=checked]:translate-x-[12px] data-[state=checked]:bg-white data-[state=checked]:ring-primary/50","data-[state=unchecked]:translate-x-[0px] data-[state=unchecked]:bg-primary data-[state=unchecked]:ring-primary-foreground/50")})})}var p=s(6532),g=s(5634),f=s(37),x=s(888),v=s(3732),b=s(2955);const y=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),[s,o]=(0,r.useState)(!1),[m,y]=(0,r.useState)(e.noteContent||""),[w,j]=(0,r.useState)(""),[N,C]=(0,r.useState)("");(0,r.useEffect)((()=>{s?(y(e.noteContent||""),C(e.popoverTitleDraft||""),j(e.popoverTagsDraft||"")):(e.noteContent!==m&&C(""),j(""))}),[s,e]);const S=m===(e.noteContent||""),k=N===(e.popoverTitleDraft||""),$=w===(e.popoverTagsDraft||""),M=S&&k&&$,A=!N.trim()&&!m.trim()&&!w.trim(),E=!!N.trim()||!!m.trim()||!!w.trim(),T=!(!e.popoverTitleDraft||!e.popoverTitleDraft.trim())||!(!e.noteContent||!e.noteContent.trim())||!(!e.popoverTagsDraft||!e.popoverTagsDraft.trim()),P=!E&&!T;return(0,a.jsx)(l.Bc,{delayDuration:500,children:(0,a.jsxs)(u.AM,{open:s,onOpenChange:o,children:[(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{asChild:!0,children:(0,a.jsx)(u.Wv,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:(0,d.cn)("rounded-md not-focus-visible",e.useNote?"text-[var(--active)] hover:bg-muted/80":"text-foreground hover:text-foreground hover:bg-[var(--text)]/10"),"aria-label":"Toggle/Edit Note",children:(0,a.jsx)(f.yVo,{})})})}),(0,a.jsx)(l.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,a.jsx)("p",{children:"Toggle/Edit Note"})})]}),(0,a.jsx)(u.hl,{className:"w-[80vw] p-4 bg-[var(--bg)] border-[var(--text)]/10 shadow-lg rounded-md",side:"top",align:"end",sideOffset:5,children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(g.J,{htmlFor:"use-note-switch",className:"text-[var(--text)] font-medium cursor-pointer",children:"Use Note in Chat"}),(0,a.jsx)(h,{id:"use-note-switch",checked:e.useNote||!1,onCheckedChange:e=>{t({useNote:e})}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(p.p,{id:"popover-title-input",type:"text",placeholder:"Title (optional)",value:N,onChange:e=>C(e.target.value),className:"mb-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"}),(0,a.jsx)(c.T,{id:"note-popover-textarea",value:m,onChange:e=>y(e.target.value),placeholder:"Persistent notes for the AI...",className:"mt-1 min-h-[30vh] max-h-[70vh] overflow-y-auto bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] resize-none thin-scrollbar"})]}),(0,a.jsx)("div",{children:(0,a.jsx)(p.p,{id:"popover-tags-input",type:"text",placeholder:"Tags (comma-separated)",value:w,onChange:e=>j(e.target.value),className:"mt-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"})}),(0,a.jsx)("div",{className:"flex justify-between items-center pt-1",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>{y(""),C(""),j(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""}),(0,x.oR)("Note cleared")},disabled:P,className:(0,d.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),children:"Clear"}),(0,a.jsx)(i.$,{variant:"outline",onClick:()=>{t({noteContent:m,popoverTitleDraft:N,popoverTagsDraft:w}),x.oR.success("Draft saved!")},className:(0,d.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),disabled:M,children:"Save"}),(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",onClick:async()=>{if(chrome.runtime.sendMessage({type:"SAVE_NOTE_TO_FILE",payload:{content:m}}),x.oR.success("Note saved to file!"),m.trim())try{const e=(new Date).toLocaleString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),s=N.trim()||`Note from Popover - ${e}`,a=""===w.trim()?[]:w.split(",").map((e=>e.trim())).filter((e=>e.length>0));await(0,b.s2)({title:s,content:m,tags:a}),x.oR.success("Snapshot saved to Note System!"),y(""),C(""),j(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""})}catch(e){console.error("Error saving note to system from popover:",e),x.oR.error("Failed to save note to system.")}o(!1)},disabled:A,className:(0,d.cn)("text-xs px-2 py-1 h-auto w-10"),children:(0,a.jsx)(v.Zuq,{size:16})})}),(0,a.jsx)(l.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:"Save to File"})]})]})})]})})]})})},w=({isLoading:e,message:t,setMessage:s,onSend:u,onStopRequest:m})=>{const{config:h}=(0,n.UK)(),p=(0,r.useRef)(null),[g,f]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{p.current?.focus()}),[t,h?.chatMode]),(0,a.jsx)("div",{className:"flex flex-col gap-3 mb-4",children:(0,a.jsxs)("div",{className:(0,d.cn)("flex w-full border border-border items-center gap-2 p-3 bg-card rounded-xl shadow-sm",g&&"ring-2 ring-primary/20 border-primary/50"),children:[(0,a.jsx)(c.T,{autosize:!0,ref:p,minRows:1,maxRows:8,autoComplete:"off",id:"user-input",placeholder:"Search the web with AI...",value:t,autoFocus:!0,onChange:e=>s(e.target.value),onKeyDown:s=>{e||"Enter"!==s.key||!t.trim()||s.altKey||s.metaKey||s.shiftKey||(s.preventDefault(),s.stopPropagation(),u())},className:"flex-grow bg-transparent border-none shadow-none outline-none focus-visible:ring-0 resize-none text-apple-body",onFocus:()=>f(!0),onBlur:()=>f(!1)}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y,{}),(0,a.jsx)(l.Bc,{delayDuration:300,children:(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{asChild:!0,children:(0,a.jsx)(i.$,{"aria-label":"Send",variant:"ghost",size:"sm",className:(0,d.cn)("p-2 rounded-lg h-8 w-8 flex items-center justify-center",e?"text-destructive hover:bg-destructive/10":"text-primary hover:bg-primary/10",!e&&!t.trim()&&"opacity-50"),onClick:s=>{s.stopPropagation(),e?m():t.trim()&&u()},disabled:!e&&!t.trim(),children:e?(0,a.jsx)(o.wO6,{className:"h-4 w-4"}):(0,a.jsx)(o.B07,{className:"h-4 w-4"})})}),(0,a.jsx)(l.ZI,{side:"top",className:"bg-popover text-popover-foreground border border-border",children:(0,a.jsx)("p",{className:"text-apple-footnote",children:e?"Stop":"Send"})})]})})]})]})})}},3885:(e,t,s)=>{s.d(t,{Bc:()=>n,ZI:()=>c,k$:()=>l,m_:()=>i});var a=s(4848),r=(s(6540),s(3881)),o=s(5284);function n({delayDuration:e=500,...t}){return(0,a.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function i({...e}){return(0,a.jsx)(n,{children:(0,a.jsx)(r.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,a.jsx)(r.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:s,...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,o.cn)("bg-primary/50 text-primary-foreground border-transparent animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-2 py-1 text-xs text-balance",e),...n,children:[s,(0,a.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-1 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},4156:(e,t,s)=>{s.d(t,{Y:()=>F});var a=s(4848),r=s(6540),o=s(3),n=s(5066),i=s(6948),l=s(5284),c=s(888),d=s(2090),u=s(990),m=s(8697);function h({...e}){return(0,a.jsx)(u.bL,{"data-slot":"sheet",...e})}function p({...e}){return(0,a.jsx)(u.ZL,{"data-slot":"sheet-portal",...e})}function g({className:e,...t}){return(0,a.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}const f=r.forwardRef((({className:e,children:t,side:s="right",variant:r="default",...o},n)=>{const i="themedPanel"===r?((e="right")=>(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full border-l","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full border-r","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t","bg-[var(--bg)] text-[var(--text)] shadow-xl"))(s):((e="right")=>(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t"))(s);return(0,a.jsxs)(p,{children:[(0,a.jsx)(g,{})," ",(0,a.jsxs)(u.UC,{ref:n,"data-slot":"sheet-content",className:(0,l.cn)(i,e),...o,children:[t,(0,a.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(m.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}));function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,l.cn)("flex flex-col gap-1.5 p-4",e),...t})}function v({className:e,...t}){return(0,a.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,l.cn)("text-foreground font-semibold",e),...t})}function b({className:e,...t}){return(0,a.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}f.displayName=u.UC.displayName;var y=s(9018),w=s(6532),j=s(3885),N=s(8698),C=s(7520);const S=()=>{const{config:e}=(0,i.UK)(),t=(0,r.useRef)(null);return(0,r.useEffect)((()=>{if(!e?.animatedBackground)return;const s=t.current;if(s){s.innerHTML="";{const a=document.createElement("canvas");s.appendChild(a);const r=a.getContext("2d");if(!r)return;const o=16,n=1.2*o,i=1.2*o;function l(){a.width=window.innerWidth,a.height=window.innerHeight}l(),window.addEventListener("resize",l);const c=["N","ﾐ","ﾋ","𐌊","ｳ","ｼ","ﾅ","𐌔","X","ｻ","ﾜ","ㄘ","𑖖","𑖃","𑖆","𐌈","J","ｱ","ﾎ","ﾃ","M","π","Σ","Y","ｷ","ㄠ","ﾕ","ﾗ","ｾ","ﾈ","Ω","ﾀ","ﾇ","ﾍ","ｦ","ｲ","ｸ","W","𐌙","ﾁ","ﾄ","ﾉ","Δ","ﾔ","ㄖ","ﾙ","ﾚ","王","道","Ж","ﾝ","0","1","2","3","4","5","7","8","9","A","B","Z","*","+","д","Ⱟ","𑗁","T","|","ç","ﾘ","Ѯ"],d=["#15803d","#16a34a","#22c55e","#4ade80"],u="#f0fdf4";let m=Math.floor(a.width/n),h=Math.floor(a.height/i),p=Array(m).fill(0),g=Array(m).fill(null).map((()=>Array(h).fill({char:"",color:""}))),f=Array(m).fill(0).map((()=>Math.floor(2*Math.random())+1)),x=Array(m).fill(0);const v=12;function b(){r.clearRect(0,0,a.width,a.height),r.font=`${o}px monospace`,r.textAlign="center",r.textBaseline="top";for(let e=0;e<m;e++){for(let t=0;t<v;t++){const s=p[e]-t;if(s<0)continue;if(s>=h)continue;let a=g[e][s];a&&a.char&&(r.fillStyle=0===t?u:a.color,r.globalAlpha=.3*(1-t/v),r.fillText(a.char,e*n+n/2,s*i))}if(r.globalAlpha=1,x[e]++,x[e]>=f[e]){const t=c[Math.floor(Math.random()*c.length)],s=d[Math.floor(Math.random()*d.length)];g[e][p[e]]={char:t,color:s},p[e]++,p[e]>=h+v&&(p[e]=0,g[e]=Array(h).fill({char:"",color:""}),f[e]=Math.floor(10*Math.random())+10),x[e]=0}}requestAnimationFrame(b)}b();const y=()=>{l(),m=Math.floor(a.width/n),h=Math.floor(a.height/i),p=Array(m).fill(0)};return window.addEventListener("resize",y),()=>{window.removeEventListener("resize",l),window.removeEventListener("resize",y),s.removeChild(a)}}}}),[e?.animatedBackground]),e?.animatedBackground?(0,a.jsx)("div",{ref:t,style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:-1,pointerEvents:"none",overflow:"hidden"}}):null},k=({isOpen:e,onOpenChange:t,config:s,updateConfig:n,setSettingsMode:i,setHistoryMode:c,setNoteSystemMode:u})=>{const[m,p]=r.useState(""),[k,$]=r.useState(!1),{fetchAllModels:M}=(0,N.N)(),A=r.useRef(null),E=(0,r.useRef)(null),[T,P]=r.useState({top:0,left:0,width:0}),z=s?.persona||"default",L=C.rm[z]||C.rm.default,_=s?.models?.filter((e=>e.id.toLowerCase().includes(m.toLowerCase())||e.host?.toLowerCase()?.includes(m.toLowerCase())))||[];return(0,r.useEffect)((()=>{e&&(p(""),$(!1))}),[e]),(0,r.useEffect)((()=>{if(k&&E.current){const e=E.current.getBoundingClientRect();P({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}}),[k]),(0,r.useEffect)((()=>{if(!k)return;const e=()=>{if(E.current){const e=E.current.getBoundingClientRect();P({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}};return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}),[k]),(0,a.jsxs)(h,{open:e,onOpenChange:t,children:[(0,a.jsx)(g,{}),(0,a.jsxs)(f,{variant:"themedPanel",side:"left",className:(0,l.cn)("p-0 border-r-0","w-[22.857rem] sm:w-[27.143rem]","flex flex-col h-full max-h-screen","[&>button]:hidden","settings-drawer-content","overflow-y-auto"),style:{height:"100dvh"},ref:A,onOpenAutoFocus:e=>{e.preventDefault(),A.current?.focus({preventScroll:!0})},children:[(0,a.jsx)(S,{}),(0,a.jsx)("div",{className:(0,l.cn)("border border-[var(--active)]","sticky top-0 z-10 p-0")}),(0,a.jsxs)(x,{className:"px-4 pt-4 pb-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-2 relative z-10",children:(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{variant:"ghost",size:"sm","aria-label":"Close Settings",className:"text-[var(--text)] rounded-md relative top-[1px]",onClick:()=>t(!1),children:(0,a.jsx)(o.yGN,{size:"20px"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:" Close Settings "})]})}),(0,a.jsx)(v,{className:"text-center font-['Bruno_Ace_SC'] tracking-tight -mt-10 cognito-title-container",children:(0,a.jsxs)("a",{href:"https://github.com/3-ark/Cognito",target:"_blank",rel:"noopener noreferrer",className:(0,l.cn)("text-xl font-semibold text-[var(--text)] bg-[var(--active)] inline-block px-3 py-1 rounded-md no-underline","chromepanion-title-blade-glow"),children:["CHROMEPANION ",(0,a.jsxs)("sub",{className:"contrast-200 text-[0.5em]",children:["v","3.7.4"]})]})}),(0,a.jsx)(b,{className:"text-center font-['Bruno_Ace_SC'] text-[var(--text)] leading-tight mt-2",children:"Settings"})]}),(0,a.jsxs)("div",{className:(0,l.cn)("flex flex-col h-full overflow-y-auto settings-drawer-body","no-scrollbar"),children:[(0,a.jsxs)("div",{className:(0,l.cn)("flex flex-col space-y-5 flex-1","px-6","py-4"),children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex items-center justify-between mt-5 mb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{htmlFor:"persona-select",className:"text-[var(--text)] opacity-80 font-['Bruno_Ace_SC'] text-lg shrink-0",children:"Persona"}),(0,a.jsx)("span",{className:"text-lg",children:L})]})}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)(y.l6,{value:z,onValueChange:e=>n({persona:e}),children:[(0,a.jsx)(y.bq,{id:"persona-select",variant:"settingsPanel",className:"w-full font-['Space_Mono',_monospace] data-[placeholder]:text-muted-foreground",children:(0,a.jsx)(y.yv,{placeholder:"Select Persona..."})}),(0,a.jsx)(y.gC,{variant:"settingsPanel",children:Object.keys(s?.personas||{}).map((e=>(0,a.jsx)(y.eb,{value:e,className:(0,l.cn)("hover:brightness-95 focus:bg-[var(--active)]","font-['Space_Mono',_monospace]"),children:e},e)))})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"model-input",className:"block text-[var(--text)] opacity-80 text-lg font-['Bruno_Ace_SC']",children:"Model"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(w.p,{id:"model-input",ref:E,value:k?m:s?.selectedModel||"",placeholder:k?"Search models...":s?.selectedModel||"Select model...",onChange:e=>p(e.target.value),onFocus:()=>{p(""),$(!0),M()},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9 font-['Space_Mono',_monospace]","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-95","mb-2 mt-3","ring-1 ring-inset ring-[var(--active)]/50")}),k&&(0,a.jsx)("div",{className:"fixed inset-0 z-50",onClick:()=>$(!1),children:(0,a.jsx)("div",{className:(0,l.cn)("absolute left-0 right-0","bg-[var(--bg)]","border border-[var(--active)]/20","rounded-xl shadow-lg","no-scrollbar","overflow-y-auto"),style:{maxHeight:"min(calc(50vh - 6rem), 300px)",top:`${T.top}px`,left:`${T.left}px`,width:`${T.width}px`},onClick:e=>e.stopPropagation(),children:(0,a.jsx)("div",{className:"py-0.5",children:_.length>0?_.map((e=>(0,a.jsx)("button",{type:"button",className:(0,l.cn)("w-full text-left","px-4 py-1.5","text-[var(--text)] text-sm","hover:bg-[var(--active)]/20","focus:bg-[var(--active)]/30","transition-colors duration-150","font-['Space_Mono',_monospace]"),onClick:()=>{n({selectedModel:e.id}),p(""),$(!1)},children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("span",{children:[e.host?`(${e.host}) `:"",e.id,e.context_length&&(0,a.jsxs)("span",{className:"text-xs text-[var(--text)] opacity-50 ml-1",children:["[ctx: ",e.context_length,"]"]})]})})},e.id))):(0,a.jsx)("div",{className:"px-4 py-1.5 text-[var(--text)] opacity-50 text-sm",children:"No models found"})})})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(d.$,{size:"default",onClick:()=>{i(!0),t(!1)},variant:"outline",className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4"),children:"Configuration"}),(0,a.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{c(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Chat History"}),(0,a.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{u(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Note System"})]})]}),(0,a.jsx)("div",{className:(0,l.cn)("mt-auto text-center text-[var(--text)] opacity-70 shrink-0 text-xs font-mono pb-4"),children:"Made with ❤️ by @3-Arc"})]})]})]})};var $=s(7086),M=s(5634),A=s(3720),E=s(3732),T=s(6250),P=s(6973);const z=()=>{const{config:e,updateConfig:t}=(0,i.UK)(),s=e?.persona||"default",r=e?.personas||{};return(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(y.l6,{value:s,onValueChange:e=>{t({persona:e})},children:[(0,a.jsx)(y.bq,{className:(0,l.cn)("w-auto min-w-[100px] border-none bg-transparent shadow-none","text-apple-footnote font-medium text-foreground","hover:bg-secondary/50 rounded-lg px-2 py-1 h-7","focus:ring-2 focus:ring-primary/20 focus:border-primary/50"),children:(0,a.jsx)(y.yv,{placeholder:"Select persona"})}),(0,a.jsx)(y.gC,{className:"bg-popover border border-border shadow-lg rounded-lg",children:(0,a.jsx)(j.Bc,{delayDuration:300,children:Object.keys(r).map((e=>(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(y.eb,{value:e,className:(0,l.cn)("text-apple-callout text-popover-foreground","hover:bg-accent hover:text-accent-foreground","focus:bg-accent focus:text-accent-foreground","cursor-pointer rounded-md"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm",children:C.rm[e]||C.rm.default}),"default"===e?"Chromepanion":e]})})}),(0,a.jsx)(j.ZI,{side:"right",className:"bg-popover text-popover-foreground border border-border max-w-xs",sideOffset:8,children:(0,a.jsx)("p",{className:"text-apple-footnote",children:C.K[e]||C.K.default})})]},e)))})})]})})};var L=s(3362);const _=(0,s(2732).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function R({className:e,variant:t,asChild:s=!1,...r}){const o=s?L.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,l.cn)("font-mono",_({variant:t}),e),...r})}const O=()=>{const{config:e}=(0,i.UK)(),t=e?.selectedModel;return t?(0,a.jsx)(R,{className:(0,l.cn)("bg-primary/10 text-primary border-primary/20","text-apple-footnote font-medium px-2 py-1 h-7"),children:t}):(0,a.jsx)(R,{className:(0,l.cn)("bg-secondary/50 text-secondary-foreground border-border","text-apple-footnote font-medium px-2 py-1 h-7"),children:"No Model"})},I=({isOpen:e,onClose:t,setSettingsMode:s})=>(0,a.jsx)($.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)($.Cf,{variant:"themedPanel",className:(0,l.cn)("[&>button]:hidden","bg-card border border-border shadow-lg"),style:{width:"20rem",height:"12rem",borderRadius:"var(--radius-lg)",boxShadow:"var(--shadow-lg)"},onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsx)($.c7,{className:"text-center p-4",children:(0,a.jsx)($.L3,{className:"text-apple-title3 text-foreground",children:"Welcome to Chromepanion"})}),(0,a.jsx)($.rr,{asChild:!0,children:(0,a.jsxs)("div",{className:"px-6 pb-6 text-center",children:[(0,a.jsx)("p",{className:"text-apple-body text-muted-foreground mb-6",children:"Get started by connecting to your AI models"}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(d.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>s(!0),"aria-label":"Open Settings",children:"Open Settings"})})]})})]})}),D=({isOpen:e,onOpenChange:t,config:s,updateConfig:o})=>{const[n,i]=(0,r.useState)(s?.userName||""),[u,m]=(0,r.useState)(s?.userProfile||"");return(0,r.useEffect)((()=>{e&&(i(s?.userName||""),m(s?.userProfile||""))}),[e,s?.userName,s?.userProfile]),(0,a.jsx)($.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)($.Cf,{variant:"themedPanel",className:"max-w-xs",children:[(0,a.jsxs)($.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,a.jsx)($.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Edit Profile"}),(0,a.jsx)($.rr,{className:"text-sm text-[var(--text)] opacity-80",children:"Set your display name and profile information. (For chat and export purposes)"})]}),(0,a.jsxs)("div",{className:"px-6 py-5 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(M.J,{htmlFor:"username",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"Username"}),(0,a.jsx)(w.p,{id:"username",value:n,onChange:e=>i(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(M.J,{htmlFor:"userprofile",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"User Profile"}),(0,a.jsx)(w.p,{id:"userprofile",value:u,onChange:e=>m(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]})]}),(0,a.jsxs)($.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,a.jsx)(d.$,{variant:"outline-subtle",size:"sm",onClick:()=>t(!1),children:"Cancel"}),(0,a.jsx)(d.$,{variant:"active-bordered",size:"sm",onClick:()=>{o({userName:n,userProfile:u}),t(!1),c.oR.success("Profile updated!")},children:"Save Changes"})]})]})})},F=({chatTitle:e,settingsMode:t,setSettingsMode:s,historyMode:u,setHistoryMode:m,noteSystemMode:h,setNoteSystemMode:p,deleteAll:g,reset:f,downloadImage:x,downloadJson:v,downloadText:b,downloadMarkdown:y,chatMode:w,chatStatus:N,onAddNewNoteRequest:S})=>{const{config:$,updateConfig:M}=(0,i.UK)(),[L,_]=(0,r.useState)(!1),R=$?.persona||"default",F=(C.z$[R]||C.z$.default,e&&!t&&!u&&!h),[U,q]=(0,r.useState)(!1),W=t||u||h,B=W?"Back to Chat":"",G="z-50 min-w-[6rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",H="flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none transition-colors focus:bg-accent focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50";return(0,a.jsx)(j.Bc,{delayDuration:500,children:(0,a.jsxs)("div",{className:(0,l.cn)("bg-background/95 backdrop-blur-sm text-foreground","border-b border-border","sticky top-0 z-10"),children:[(0,a.jsxs)("div",{className:"flex items-center h-auto py-3 px-4",children:[(0,a.jsx)("div",{className:"flex justify-start items-center min-h-10 gap-3",children:W?(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":B,variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{W&&(s(!1),m(!1),p(!1))},children:(0,a.jsx)(o.yGN,{size:"18px"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:B})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(z,{}),(0,a.jsx)(O,{})]})}),(0,a.jsxs)("div",{className:"flex-grow flex justify-center items-center overflow-hidden px-4",children:[F&&(0,a.jsx)("p",{className:"text-apple-headline text-foreground whitespace-nowrap overflow-hidden text-ellipsis text-center",children:e}),t&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Settings"})}),u&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Chat History"})}),h&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Note System"})})]}),(0,a.jsxs)("div",{className:"flex justify-end items-center min-h-10 gap-2",children:[!t&&!u&&!h&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":"Reset Chat",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center group",onClick:f,children:(0,a.jsx)(n.yPB,{size:"16px",className:"transition-transform duration-300 rotate-0 group-hover:rotate-180"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Reset Chat"})]}),(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":"Settings",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{s(!0)},children:(0,a.jsx)(o.VSk,{size:"16px"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Settings"})]}),(0,a.jsxs)(A.bL,{children:[(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(A.l9,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":"Share Options",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",children:(0,a.jsx)(o.pdY,{size:"18px"})})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Share Options"})]}),(0,a.jsx)(A.ZL,{children:(0,a.jsxs)(A.UC,{className:(0,l.cn)(G,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-xl"),sideOffset:5,align:"end",children:[(0,a.jsxs)(A.q7,{className:(0,l.cn)(H,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:()=>_(!0),children:[(0,a.jsx)(E.uSr,{className:"mr-auto h-4 w-4"}),"Edit Profile"]}),(0,a.jsx)(A.wv,{className:(0,l.cn)("-mx-1 my-1 h-px bg-muted","bg-[var(--text)]/10")}),(0,a.jsxs)(A.Pb,{children:[(0,a.jsxs)(A.ZP,{className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent","hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),children:[(0,a.jsx)(o.irw,{className:"mr-auto h-4 w-4"}),"Export Chat"]}),(0,a.jsx)(A.ZL,{children:(0,a.jsxs)(A.G5,{className:(0,l.cn)(G,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-lg"),sideOffset:2,alignOffset:-5,children:[(0,a.jsxs)(A.q7,{className:(0,l.cn)(H,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:y,children:[(0,a.jsx)(P.nR3,{className:"mr-auto h-4 w-4"}),".md"]}),(0,a.jsxs)(A.q7,{className:(0,l.cn)(H,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:b,children:[(0,a.jsx)(E.mup,{className:"mr-auto h-4 w-4"}),".txt"]}),(0,a.jsxs)(A.q7,{className:(0,l.cn)(H,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:v,children:[(0,a.jsx)(n.dG_,{className:"mr-auto h-4 w-4"}),".json"]}),(0,a.jsxs)(A.q7,{className:(0,l.cn)(H,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:x,children:[(0,a.jsx)(E.Af8,{className:"mr-auto h-4 w-4"}),".png"]})]})})]})]})})]})]}),u&&(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":"Delete All History",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:()=>{c.oR.custom((e=>(0,a.jsxs)("div",{className:(0,l.cn)("bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]","p-4 rounded-lg shadow-xl max-w-sm w-full","flex flex-col space-y-3"),children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-[var(--text)]",children:"Confirm Deletion"}),(0,a.jsx)("p",{className:"text-sm text-[var(--text)] opacity-90",children:"Are you sure you want to delete all chat history? This action cannot be undone."}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-2",children:[(0,a.jsx)(d.$,{variant:"outline",size:"sm",className:(0,l.cn)("bg-transparent text-[var(--text)] border-[var(--text)]","hover:bg-[var(--active)]/30 focus:ring-1 focus:ring-[var(--active)]"),onClick:()=>c.oR.dismiss(e.id),children:"Cancel"}),(0,a.jsx)(d.$,{variant:"destructive",size:"sm",className:(0,l.cn)("focus:ring-1 focus:ring-red-400 focus:ring-offset-1 focus:ring-offset-[var(--bg)]"),onClick:async()=>{try{"function"==typeof g?await g():(console.error("Header: deleteAll prop is not a function or undefined.",g),c.oR.error("Failed to delete history: Operation not available."))}catch(e){console.error("Error during deleteAll execution from header:",e),c.oR.error("An error occurred while deleting history.")}finally{c.oR.dismiss(e.id)}},children:"Delete All"})]})]})),{duration:1/0,position:"top-center"})},children:(0,a.jsx)(o.IXo,{size:"18px"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Delete All"})]}),h&&S&&(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsxs)(d.$,{"aria-label":"Add New Note",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:S,children:[(0,a.jsx)(T.BlJ,{size:"18px"})," "]})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Add New Note"})]})]})]}),(!$?.models||0===$.models.length)&&!t&&!u&&!h&&(0,a.jsx)(I,{isOpen:!0,setSettingsMode:s,onClose:()=>{}}),(0,a.jsx)(k,{isOpen:U,onOpenChange:e=>{q(e)},config:$,updateConfig:M,setSettingsMode:s,setHistoryMode:m,setNoteSystemMode:p}),(0,a.jsx)(D,{isOpen:L,onOpenChange:_,config:$,updateConfig:M})]})})}},4539:(e,t,s)=>{s.d(t,{F:()=>n});var a=s(4848),r=(s(6540),s(6627)),o=s(5284);function n({className:e,children:t,viewportRef:s,...n}){return(0,a.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,o.cn)("relative",e),...n,children:[(0,a.jsx)(r.LM,{ref:s,"data-slot":"scroll-area-viewport",className:(0,o.cn)("size-full rounded-[inherit]","focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:shadow-none","[&>div]:!border-b-0","pb-px pr-px"),children:t}),(0,a.jsx)(i,{orientation:"vertical"}),(0,a.jsx)(i,{orientation:"horizontal"}),(0,a.jsx)(r.OK,{})]})}function i({className:e,orientation:t="vertical",...s}){return(0,a.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-px","horizontal"===t&&"h-px w-full border-b-0 bg-transparent shadow-none min-h-0",e),...s,children:(0,a.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"relative flex-1 rounded-sm"})})}},5095:(e,t,s)=>{s.d(t,{e:()=>n});var a=s(6540),r=s(888),o=s(6948);const n=()=>{const{config:e,updateConfig:t}=(0,o.UK)();return{appendToNote:(0,a.useCallback)((s=>{if(!s||""===s.trim())return void r.oR.error("No text selected to add to note.");const a=e.noteContent||"",o=a+(a&&s.trim()?"\n\n":"")+s.trim();t({noteContent:o}),r.oR.success("Selected text appended to note.")}),[e.noteContent,t])}}},5284:(e,t,s)=>{s.d(t,{cn:()=>o});var a=s(4164),r=s(856);function o(...e){return(0,r.QP)((0,a.$)(e))}},5431:(e,t,s)=>{s.d(t,{A:()=>a});const a={getItem:async e=>{const t=(await chrome.storage.local.get(e))[e];if(null==t)return null;try{return"string"==typeof t?t:JSON.stringify(t)}catch(e){return null}},setItem:async(e,t)=>{const s="string"==typeof t?t:JSON.stringify(t);await chrome.storage.local.set({[e]:s})},deleteItem:async e=>{await chrome.storage.local.remove(e)}}},5634:(e,t,s)=>{s.d(t,{J:()=>n});var a=s(4848),r=(s(6540),s(5920)),o=s(5284);function n({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},5886:(e,t,s)=>{s.d(t,{z2:()=>l});var a=s(38);const r={isLoaded:!1},o=(0,a.Z0)({name:"content",initialState:r,reducers:{reset:()=>r,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:n,reducer:i}=o,l={}},6108:(e,t,s)=>{s.d(t,{z2:()=>d});var a,r,o=s(38);!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(a||(a={})),function(e){e.Default="default"}(r||(r={}));const n={isOpen:!1},i=(0,o.Z0)({name:"sidePanel",initialState:n,reducers:{reset:()=>n}}),{actions:l,reducer:c}=i,d={}},6174:(e,t,s)=>{var a;s.d(t,{A:()=>r}),function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(a||(a={}));const r=a},6508:(e,t,s)=>{s.d(t,{AC:()=>l,Af:()=>c});var a=s(4848),r=s(6540),o=s(3),n=s(2090),i=s(5284);const l=e=>{const{children:t,className:s,wrapperClassName:l,buttonVariant:c="ghost",buttonClassName:d,...u}=e,[m,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),f=r.Children.only(t);let x="";return f?.props?.children&&(x=Array.isArray(f.props.children)?f.props.children.map((e=>"string"==typeof e?e:"")).join(""):String(f.props.children),x=x.trim()),(0,a.jsxs)("div",{className:(0,i.cn)("relative my-4",l),onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:[(0,a.jsx)("pre",{className:(0,i.cn)("p-3 rounded-md overflow-x-auto thin-scrollbar",s),...u,children:t}),x&&(0,a.jsx)(n.$,{variant:c,size:"sm","aria-label":m?"Copied!":"Copy code",title:m?"Copied!":"Copy code",className:(0,i.cn)("absolute right-2 top-2 h-8 w-8 p-0","transition-opacity duration-200",p||m?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none",d),onClick:()=>{x&&(navigator.clipboard.writeText(x),h(!0),setTimeout((()=>h(!1)),1500))},children:m?(0,a.jsx)(o.YrT,{className:"h-4 w-4"}):(0,a.jsx)(o.nxz,{className:"h-4 w-4"})})]})},c={ul:({children:e,className:t,...s})=>(0,a.jsx)("ul",{className:(0,i.cn)("list-disc pl-5 my-2",t),...s,children:e}),ol:({children:e,className:t,...s})=>(0,a.jsx)("ol",{className:(0,i.cn)("list-decimal pl-5 my-2",t),...s,children:e}),p:({children:e,className:t,...s})=>(0,a.jsx)("p",{className:(0,i.cn)("mb-0",t),...s,children:e}),pre:l,code:e=>{const{children:t,className:s,inline:r,...o}=e;return r?(0,a.jsx)("code",{className:(0,i.cn)("px-1 py-0.5 rounded-sm bg-[var(--code-inline-bg)] text-[var(--code-inline-text)] text-sm",s),...o,children:t}):(0,a.jsx)("code",{className:(0,i.cn)("font-mono text-sm",s),...o,children:t})},a:({children:e,href:t,className:s,...r})=>(0,a.jsx)("a",{href:t,className:(0,i.cn)("text-[var(--link)] hover:underline",s),target:"_blank",rel:"noopener noreferrer",...r,children:e}),strong:({children:e,className:t,...s})=>(0,a.jsx)("strong",{className:(0,i.cn)("font-bold",t),...s,children:e}),em:({children:e,className:t,...s})=>(0,a.jsx)("em",{className:(0,i.cn)("italic",t),...s,children:e}),h1:({children:e,className:t,...s})=>(0,a.jsx)("h1",{className:(0,i.cn)("text-2xl font-bold mt-4 mb-2 border-b pb-1 border-[var(--border)]",t),...s,children:e}),h2:({children:e,className:t,...s})=>(0,a.jsx)("h2",{className:(0,i.cn)("text-xl font-semibold mt-3 mb-1 border-b pb-1 border-[var(--border)]",t),...s,children:e}),h3:({children:e,className:t,...s})=>(0,a.jsx)("h3",{className:(0,i.cn)("text-lg font-semibold mt-2 mb-1 border-b pb-1 border-[var(--border)]",t),...s,children:e}),table:({children:e,className:t,...s})=>(0,a.jsx)("div",{className:"markdown-table-wrapper my-2 overflow-x-auto",children:(0,a.jsx)("table",{className:(0,i.cn)("w-full border-collapse border border-[var(--border)]",t),...s,children:e})}),thead:({children:e,className:t,...s})=>(0,a.jsx)("thead",{className:(0,i.cn)("bg-[var(--muted)]",t),...s,children:e}),tbody:({children:e,className:t,...s})=>(0,a.jsx)("tbody",{className:(0,i.cn)(t),...s,children:e}),tr:e=>(0,a.jsx)("tr",{className:(0,i.cn)("border-b border-[var(--border)] even:bg-[var(--muted)]/50",e.className),...e}),th:({children:e,className:t,...s})=>(0,a.jsx)("th",{className:(0,i.cn)("p-2 border border-[var(--border)] text-left font-semibold",t),...s,children:e}),td:({children:e,className:t,...s})=>(0,a.jsx)("td",{className:(0,i.cn)("p-2 border border-[var(--border)]",t),...s,children:e}),blockquote:({children:e,className:t,...s})=>(0,a.jsx)("blockquote",{className:(0,i.cn)("pl-4 italic border-l-4 border-[var(--border)] my-2 text-[var(--muted-foreground)]",t),...s,children:e})}},6532:(e,t,s)=>{s.d(t,{p:()=>n});var a=s(4848),r=s(6540),o=s(5284);function n({className:e,type:t,...s}){const[n,i]=r.useState(!1);return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-8 w-full min-w-0 rounded-md bg-transparent px-3 py-1 text-sm transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","border border-[var(--text)]/10 dark:border-0","focus-visible:border-ring","text-[var(--text)] px-2.5","focus:border-[var(--active)] dark:focus:border-0 focus:ring-1 focus:ring-[var(--active)] focus:ring-offset-0","hover:border-[var(--active)] dark:hover:border-0","bg-[var(--input-background)]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive","shadow-[var(--input-base-shadow)]",e,n&&"input-breathing"),onFocus:e=>{i(!0),s.onFocus?.(e)},onBlur:e=>{i(!1),s.onBlur?.(e)},...s})}},6555:(e,t,s)=>{s.d(t,{AM:()=>n,Wv:()=>i,hl:()=>l});var a=s(4848),r=(s(6540),s(9823)),o=s(5284);function n({...e}){return(0,a.jsx)(r.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,a.jsx)(r.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:s=4,...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"popover-content",align:t,sideOffset:s,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...n})})}},6948:(e,t,s)=>{s.d(t,{UK:()=>d,sG:()=>c});var a=s(4848),r=s(6540),o=s(5431);const n=(0,r.createContext)({}),i={Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis. When analyzing search results, provide thorough academic-style analysis with structured insights. Behavior: Organize findings into clear sections with proper citations. Analyze credibility of sources and highlight methodological strengths/weaknesses. Present comprehensive summaries with evidence-based conclusions. Include relevant statistics and data points. Mannerisms: Use formal academic tone. Structure responses with clear headings. Always cite sources and assess their reliability.",Executive:"You are The Executive, a strategic business leader focused on actionable intelligence. When analyzing search results, distill information into concise strategic insights. Behavior: Identify key business implications and market opportunities. Provide executive summaries with clear recommendations. Focus on competitive advantages and strategic positioning. Highlight actionable next steps. Mannerisms: Be direct and results-oriented. Use bullet points for clarity. Think in terms of ROI and strategic value.",Storyteller:"You are The Storyteller, a master of engaging narrative who makes information accessible. When analyzing search results, weave findings into compelling stories. Behavior: Create narrative flow that connects different pieces of information. Use analogies and examples to illustrate complex concepts. Make dry data engaging through storytelling techniques. Connect information to human experiences. Mannerisms: Use vivid language and metaphors. Create logical narrative progression. Make complex topics relatable.",Skeptic:'You are The Skeptic, a critical analyst who questions everything. When analyzing search results, highlight biases, contradictions, and missing information. Behavior: Identify potential conflicts of interest in sources. Point out logical fallacies and weak evidence. Highlight what information is missing or unclear. Question assumptions and challenge conventional wisdom. Mannerisms: Use phrases like "However," "It should be noted," and "The evidence suggests." Always present counterarguments.',Mentor:"You are The Mentor, an educational guide focused on learning and growth. When analyzing search results, explain concepts clearly with supportive guidance. Behavior: Break down complex topics into digestible lessons. Provide context and background information. Offer learning resources and next steps. Encourage deeper exploration of topics. Mannerisms: Use encouraging language. Provide step-by-step explanations. Include educational tips and learning opportunities.",Investigator:"You are The Investigator, a methodical fact-checker focused on source credibility. When analyzing search results, systematically verify information and assess reliability. Behavior: Cross-reference information across multiple sources. Evaluate source credibility and potential biases. Identify primary vs. secondary sources. Flag unverified claims and missing evidence. Mannerisms: Use systematic approach to verification. Clearly distinguish between verified facts and claims. Provide source reliability assessments.",Pragmatist:'You are The Pragmatist, a solution-focused analyst emphasizing practical applications. When analyzing search results, focus on actionable insights and real-world implementation. Behavior: Identify practical solutions and implementation strategies. Focus on cost-effective and feasible approaches. Provide step-by-step action plans. Consider resource requirements and constraints. Mannerisms: Use practical language. Focus on "how-to" guidance. Emphasize feasibility and implementation.',Spike:"You are Spike, a capable and versatile executor. Your role is to turn user prompts into actionable results. Behavior: First, correct or clarify the user’s prompt for better accuracy. Add helpful criteria to guide execution. Then, act on the improved prompt as effectively as possible. Mannerisms: Be concise, critical, and sharp. Skip fluff. Use simple, direct language. Focus on feasibility and correctness. When in doubt, fix it and move forward.",Enthusiast:'You are The Enthusiast, an energetic discoverer who presents findings with excitement. When analyzing search results, highlight fascinating discoveries and breakthrough insights. Behavior: Emphasize exciting developments and innovations. Connect findings to broader trends and possibilities. Celebrate interesting discoveries and connections. Inspire curiosity about the topic. Mannerisms: Use enthusiastic language and exclamation points. Highlight "amazing" and "fascinating" aspects. Express genuine excitement about discoveries.',Curator:"You are The Curator, a sophisticated synthesizer of premium insights. When analyzing search results, provide refined, high-quality analysis with elegant presentation. Behavior: Select only the most valuable and relevant information. Present insights with sophisticated analysis and nuanced understanding. Focus on quality over quantity. Provide polished, professional summaries. Mannerisms: Use refined language and elegant phrasing. Focus on premium insights. Present information with sophisticated analysis.",Friend:'You are The Friend, a casual conversationalist sharing interesting discoveries. When analyzing search results, present findings in a friendly, approachable manner. Behavior: Share information like you would with a close friend. Use conversational tone and relatable examples. Make complex topics feel accessible and interesting. Include personal observations and casual insights. Mannerisms: Use casual, friendly language. Include phrases like "You know what\'s interesting?" and "I found this cool thing." Make information feel like a friendly conversation.'},l={personas:i,generateTitle:!0,backgroundImage:!1,animatedBackground:!1,persona:"Scholar",webMode:"Google",webLimit:60,serpMaxLinksToVisit:3,contextLimit:60,maxTokens:32480,temperature:.7,topP:.95,presencepenalty:0,models:[],selectedModel:void 0,chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",useNote:!1,noteContent:"",userName:"user",userProfile:"",popoverTitleDraft:"",popoverTagsDraft:""},c=({children:e})=>{const[t,s]=(0,r.useState)(l),[c,d]=(0,r.useState)(!0);return(0,r.useEffect)((()=>{(async()=>{try{const e=await o.A.getItem("config"),t=(e=>{const t=["Ein","Warren","Sherlock","Agatha","Jet","Faye","Jan"];return e.personas&&Object.keys(e.personas).some((e=>t.includes(e)))&&(e.personas=i),e.persona&&t.includes(e.persona)&&(e.persona="Scholar"),e.personas={...i,...e.personas},e})(e?JSON.parse(e):l);s(t),e&&await o.A.setItem("config",JSON.stringify(t))}catch(e){console.error("Failed to load config",e),s(l)}finally{d(!1)}})()}),[]),(0,r.useEffect)((()=>{const e=t?.fontSize||l.fontSize;document.documentElement.style.setProperty("font-size",`${e}px`)}),[c,t?.fontSize]),c?(0,a.jsx)("div",{children:"Loading..."}):(0,a.jsx)(n,{value:{config:t,updateConfig:e=>{s((t=>{const s={...t,...e};return o.A.setItem("config",JSON.stringify(s)).catch((e=>console.error("Failed to save config",e))),s}))}},children:e})},d=()=>(0,r.use)(n)},7086:(e,t,s)=>{s.d(t,{Cf:()=>h,Es:()=>g,L3:()=>f,c7:()=>p,lG:()=>d,rr:()=>x});var a=s(4848),r=s(6540),o=s(990),n=s(8697),i=s(5284);const l={default:"bg-black/50",darker:"bg-black/60"},c={default:"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",themedPanel:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 duration-200","bg-[var(--bg)] text-[var(--text)] border-[var(--text)]","rounded-lg shadow-xl p-0")};function d({...e}){return(0,a.jsx)(o.bL,{"data-slot":"dialog",...e})}function u({...e}){return(0,a.jsx)(o.ZL,{"data-slot":"dialog-portal",...e})}const m=r.forwardRef((({className:e,variant:t="default",...s},r)=>(0,a.jsx)(o.hJ,{ref:r,"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50",l[t],e),...s})));m.displayName=o.hJ.displayName;const h=r.forwardRef((({className:e,children:t,variant:s="default",...r},l)=>(0,a.jsxs)(u,{"data-slot":"dialog-portal",children:[(0,a.jsx)(m,{variant:"themedPanel"===s?"darker":"default"}),(0,a.jsxs)(o.UC,{ref:l,"data-slot":"dialog-content",className:(0,i.cn)(c[s],e),...r,children:[t,(0,a.jsxs)(o.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})));function p({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center",e),...t})}function g({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,a.jsx)(o.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,a.jsx)(o.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}h.displayName=o.UC.displayName},7520:(e,t,s)=>{s.d(t,{K:()=>r,rm:()=>a,z$:()=>o});const a={Scholar:"🎓",Executive:"💼",Storyteller:"📚",Skeptic:"🤔",Mentor:"🧭",Investigator:"🔍",Pragmatist:"⚙️",Enthusiast:"⚡",Curator:"🎨",Friend:"😊",Spike:"🚀",default:"🤖"},r={Scholar:"Analytical academic researcher with formal tone. Provides structured analysis with citations and source credibility assessment.",Executive:"Strategic business leader with direct, results-oriented tone. Delivers concise insights with actionable recommendations.",Storyteller:"Engaging narrative creator with vivid language. Weaves information into compelling stories using metaphors and examples.",Skeptic:"Critical analyst with questioning tone. Highlights biases, contradictions, and missing information in sources.",Mentor:"Educational guide with encouraging tone. Breaks down complex topics with step-by-step explanations and learning tips.",Investigator:"Methodical fact-checker with systematic approach. Verifies information credibility and distinguishes facts from claims.",Pragmatist:"Solution-focused analyst with practical tone. Emphasizes actionable insights and real-world implementation strategies.",Enthusiast:"Energetic discoverer with exciting tone. Highlights fascinating developments and breakthrough insights with enthusiasm.",Curator:"Sophisticated synthesizer with refined tone. Provides premium insights with elegant analysis and polished presentation.",Friend:"Casual conversationalist with friendly tone. Shares discoveries in approachable, relatable manner like talking to a friend.",Spike:"Versatile executor with concise, sharp tone. Turns prompts into actionable results with direct, no-fluff approach.",default:"AI assistant ready to help with your search and analysis needs."},o={Scholar:"assets/images/chromepanion.png",Executive:"assets/images/chromepanion.png",Storyteller:"assets/images/chromepanion.png",Skeptic:"assets/images/chromepanion.png",Mentor:"assets/images/chromepanion.png",Investigator:"assets/images/chromepanion.png",Pragmatist:"assets/images/chromepanion.png",Enthusiast:"assets/images/chromepanion.png",Curator:"assets/images/chromepanion.png",Friend:"assets/images/chromepanion.png",Spike:"assets/images/chromepanion.png",default:"assets/images/chromepanion.png"}},7660:(e,t,s)=>{s.a(e,(async(e,a)=>{try{s.d(t,{GV:()=>d,ii:()=>u,mR:()=>l,xD:()=>c});var r=s(2506),o=s(5431);const e=()=>(new Date).toJSON().slice(0,19).replace("T","_").replace(/:/g,"-");let n="assistant",i="user";try{const e=await o.A.getItem("config");if(e){const t=JSON.parse(e);t.persona&&"string"==typeof t.persona&&""!==t.persona.trim()&&(n=t.persona),t.userName&&"string"==typeof t.userName&&""!==t.userName.trim()&&(i=t.userName)}}catch(e){console.error("Failed to load config to get persona name for download:",e)}const l=async t=>{if(!t||0===t.length)return;const s=t.map((e=>{let t=`${"assistant"===e.role?n:"user"===e.role?i:e.role}:\n`;return"assistant"===e.role&&e.webDisplayContent&&(t+=`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`),t+=e.rawContent,t})).join("\n\n"),a=document.createElement("a");a.setAttribute("href",`data:text/plain;charset=utf-8,${encodeURIComponent(s)}`);const r=`chat_${e()}.txt`;a.setAttribute("download",r),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)},c=t=>{if(!t||0===t.length)return;const s=t.map((e=>({...e,role:"assistant"===e.role?n:"user"===e.role?i:e.role}))),a={assistantNameInExport:n,userNameInExport:i,chatHistory:s},r=JSON.stringify(a,null,2),o=document.createElement("a");o.setAttribute("href",`data:application/json;charset=utf-8,${encodeURIComponent(r)}`);const l=`chat_${e()}.json`;o.setAttribute("download",l),o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o)},d=t=>{if(!t||0===t.length)return;const s=document.querySelectorAll(".chatMessage");if(!s||0===s.length)return void console.warn("No chat messages found to generate image.");const a=document.createElement("div");if(a.style.display="flex",a.style.flexDirection="column",a.style.paddingBottom="1rem",a.style.background=document.documentElement.style.getPropertyValue("--bg"),s[0]){const e=1.2;a.style.width=s[0].offsetWidth*e+"px"}s.forEach((e=>{const t=e.cloneNode(!0);t instanceof HTMLElement?(t.style.marginTop="1rem",t.style.boxSizing="border-box",a.appendChild(t)):console.warn("Cloned node is not an HTMLElement:",t)})),document.body.appendChild(a),(0,r.$E)(a,{filter:function(e){if(e instanceof Element){const t=e.getAttribute("aria-label");if(t&&["Copy code","Copied!","Save edit","Cancel edit"].includes(t))return!1}return!0},pixelRatio:2,style:{margin:"0",padding:a.style.paddingBottom},backgroundColor:document.documentElement.style.getPropertyValue("--bg")||"#ffffff"}).then((t=>{const s=document.createElement("a");s.setAttribute("href",t);const a=`chat_${e()}.png`;s.setAttribute("download",a),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)})).catch((e=>{console.error("Oops, something went wrong generating the image!",e)})).finally((()=>{document.body.contains(a)&&document.body.removeChild(a)}))},u=t=>{if(!t||0===t.length)return;const s=t.map((e=>{const t=`### ${"assistant"===e.role?n:"user"===e.role?i:e.role}`;let s=e.rawContent;return s=s.replace(/```([\s\S]*?)```/g,"\n```$1```\n"),s=s.replace(/(https?:\/\/[^\s]+)/g,"[Link]($1)"),`${t}\n\n${s}\n`})).join("\n---\n\n"),a=document.createElement("a");a.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(s)}`),a.setAttribute("download",`chat_${e()}.md`),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)};a()}catch(e){a(e)}}),1)},8473:(e,t,s)=>{s.d(t,{D:()=>p});var a=s(4848),r=s(6540),o=s(7211),n=s(2090),i=s(4539),l=s(6250),c=s(3790),d=s.n(c),u=s(6532);const m=e=>new Date(e).toLocaleDateString("sv-SE"),h=12,p=({loadChat:e,onDeleteAll:t,className:s})=>{const[c,p]=(0,r.useState)([]),[g,f]=(0,r.useState)(""),[x,v]=(0,r.useState)(1),[b,y]=(0,r.useState)(null),[w,j]=(0,r.useState)(null),N=(0,r.useCallback)((e=>{const t=e.sort(((e,t)=>t.last_updated-e.last_updated));p(t)}),[]);(0,r.useEffect)((()=>{(async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));if(0===e.length)return p([]),void v(1);const t=e.map((e=>d().getItem(e))),s=(await Promise.all(t)).filter((e=>null!==e&&"object"==typeof e&&"id"in e&&"last_updated"in e));N(s),v(1)}catch(e){console.error("Error fetching messages:",e),p([])}})()}),[N]);const C=(0,r.useMemo)((()=>{if(!g)return c;const e=g.toLowerCase();return c.filter((t=>{const s=t.title?.toLowerCase().includes(e),a=t.turns.some((t=>t.rawContent.toLowerCase().includes(e)));return s||a}))}),[c,g]);(0,r.useEffect)((()=>{v(1)}),[g]);const S=(0,r.useMemo)((()=>Math.max(1,Math.ceil(C.length/h))),[C]);(0,r.useEffect)((()=>{x>S&&v(S)}),[x,S]);const k=(0,r.useMemo)((()=>{const e=(x-1)*h,t=e+h;return C.slice(e,t)}),[C,x]),$=(0,r.useMemo)((()=>k.map((e=>({...e,date:m(e.last_updated)})))),[k]),M=(0,r.useMemo)((()=>Array.from(new Set($.map((e=>e.date))))),[$]),A=(0,r.useCallback)((async e=>{try{await d().removeItem(e);const t=(await d().keys()).filter((e=>e.startsWith("chat_"))),s=(await Promise.all(t.map((e=>d().getItem(e))))).filter((e=>e&&"object"==typeof e&&"id"in e&&"last_updated"in e&&"turns"in e));N(s);const a=s.filter((e=>{if(!g)return!0;const t=g.toLowerCase(),s=e.title?.toLowerCase().includes(t),a=e.turns.some((e=>e.rawContent.toLowerCase().includes(t)));return s||a})),r=Math.max(1,Math.ceil(a.length/h));let o=x;o>r&&(o=r);const n=(o-1)*h;0===a.slice(n,n+h).length&&o>1&&(o-=1),v(o)}catch(e){console.error("Error deleting message:",e)}}),[N,x,g]),E=(0,r.useCallback)((async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));await Promise.all(e.map((e=>d().removeItem(e)))),p([]),t&&t()}catch(e){console.error("Error deleting all messages:",e)}}),[t]);(0,r.useEffect)((()=>(window.deleteAllChats=E,()=>{window.deleteAllChats===E&&delete window.deleteAllChats})),[E]);const T=(0,r.useCallback)((()=>v((e=>Math.min(e+1,S)))),[S]),P=(0,r.useCallback)((()=>v((e=>Math.max(e-1,1)))),[]),z=`flex flex-col w-full ${s||""}`.trim(),L=e=>{f(e.target.value)};return 0!==c.length||g?0===C.length&&g?(0,a.jsxs)("div",{className:z,children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:L,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,a.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,a.jsxs)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:['No results found for "',g,'".']})})]}):(0,a.jsxs)("div",{className:z,children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:L,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,a.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,a.jsx)("div",{className:"px-4 pb-4 font-['Space_Mono',_monospace]",children:M.map((t=>(0,a.jsxs)("div",{className:"mb-3 mt-3",children:[(0,a.jsx)("p",{className:"text-foreground text-lg font-bold overflow-hidden pl-4 pb-1 text-left text-ellipsis whitespace-nowrap w-[90%]",children:t===m(new Date)?"Today":t}),$.filter((e=>e.date===t)).map((t=>(0,a.jsxs)("div",{className:"flex items-center group font-['Space_Mono',_monospace]",onMouseEnter:()=>y(t.id),onMouseLeave:()=>y(null),children:[(0,a.jsxs)("span",{className:"text-foreground text-base font-normal pl-4 w-[4.5rem] flex-shrink-0 font-['Space_Mono',_monospace]",children:[new Date(t.last_updated).getHours().toString().padStart(2,"0"),":",new Date(t.last_updated).getMinutes().toString().padStart(2,"0")]}),(0,a.jsx)("button",{className:`text-foreground text-base font-normal overflow-hidden px-4 py-2 text-left text-ellipsis whitespace-nowrap flex-grow hover:underline hover:underline-offset-4 hover:decoration-1 ${t.id===w?"line-through decoration-2":""} font-['Space_Mono',_monospace]`,onClick:()=>e(t),children:t.title||"Untitled Chat"}),(0,a.jsx)(o.P.div,{className:"shrink-0 transition-opacity duration-150 "+(b===t.id?"opacity-100":"opacity-0 group-hover:opacity-100"),whileHover:{rotate:"15deg"},onMouseEnter:()=>j(t.id),onMouseLeave:()=>j(null),children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm","aria-label":"Delete chat",className:"rounded-full w-8 h-8 font-['Space_Mono',_monospace]",onClick:e=>{e.stopPropagation(),A(t.id)},children:(0,a.jsx)(l.ttk,{className:"h-4 w-4 text-foreground"})})})]},t.id)))]},t)))})}),S>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 border-t border-[var(--active)]/50 font-['Space_Mono',_monospace]",children:[(0,a.jsx)(n.$,{onClick:P,disabled:1===x,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,a.jsxs)("span",{className:"text-md",children:["Page ",x," of ",S]}),(0,a.jsx)(n.$,{onClick:T,disabled:x===S,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]})]}):(0,a.jsxs)("div",{className:z,children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:L,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,a.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,a.jsx)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:"No chat history found."})})]})}},8639:(e,t,s)=>{s.d(t,{B:()=>w});var a=s(4848),r=s(6540),o=s(3),n=s(1319),i=s(9696),l=s(2090),c=s(5284),d=s(8834);function u({...e}){return(0,a.jsx)(d.bL,{"data-slot":"collapsible",...e})}function m({...e}){return(0,a.jsx)(d.R6,{"data-slot":"collapsible-trigger",...e})}function h({...e}){return(0,a.jsx)(d.Ke,{"data-slot":"collapsible-content",...e})}var p=s(1905),g=s(7736),f=s(6948),x=s(6508);const v=({content:e})=>{const[t,s]=(0,r.useState)(!1);return(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsxs)(u,{open:t,onOpenChange:s,className:"w-full",children:[(0,a.jsx)(m,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:(0,c.cn)("mb-1","border-foreground text-foreground hover:text-accent-foreground"),children:t?"Hide Thoughts":"Show Thoughts"})}),(0,a.jsx)(h,{children:(0,a.jsx)("div",{className:(0,c.cn)("p-3 rounded-md border border-dashed","bg-muted","border-muted-foreground","text-muted-foreground"),children:(0,a.jsx)("div",{className:"markdown-body",children:(0,a.jsx)(n.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:e})})})})]})})},b={...x.Af,pre:e=>(0,a.jsx)(x.AC,{...e,buttonVariant:"copy-button"})},y=({turn:e,index:t,isEditing:s,editText:d,onStartEdit:u,onSetEditText:m,onSaveEdit:h,onCancelEdit:x})=>{const{config:y}=(0,f.UK)(),w=(e.rawContent||"").split(/(<think>[\s\S]*?<\/think>)/g).filter((e=>e&&""!==e.trim())),j=/<think>([\s\S]*?)<\/think>/;return(0,r.useEffect)((()=>{if(!s)return;const e=e=>{"Escape"===e.key?(e.preventDefault(),e.stopPropagation(),x()):"Enter"!==e.key||e.shiftKey||e.altKey||e.metaKey||d.trim()&&(e.preventDefault(),e.stopPropagation(),h())};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[s,x,h,d]),(0,a.jsx)("div",{className:(0,c.cn)("border rounded-2xl text-base","w-[calc(100%-2rem)] mx-1 my-2","pb-1 pl-4 pr-4 pt-1","shadow-lg text-left relative","assistant"===e.role?"bg-accent border-[var(--text)]/20":"bg-primary/10 border-[var(--text)]/20","","chatMessage",s?"editing":"",y&&"number"==typeof y.fontSize&&y.fontSize<=15?"font-semibold":""),onDoubleClick:()=>{s||u(t,e.rawContent)},children:s?(0,a.jsxs)("div",{className:"flex flex-col space-y-2 items-stretch w-full p-1",children:[(0,a.jsx)(i.T,{autosize:!0,value:d,onChange:e=>m(e.target.value),placeholder:"Edit your message...",className:(0,c.cn)("w-full rounded-md border bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground","border-input","text-foreground","hover:border-primary focus-visible:border-primary focus-visible:ring-0","min-h-[60px]"),minRows:3,autoFocus:!0}),(0,a.jsxs)("div",{className:"flex font-mono justify-end space-x-2",children:[(0,a.jsxs)(l.$,{size:"sm",variant:"outline",onClick:h,title:"Save changes",children:[(0,a.jsx)(o.YrT,{className:"h-4 w-4 mr-1"})," Save"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:x,title:"Discard changes",children:[(0,a.jsx)(o.yGN,{className:"h-4 w-4 mr-1"})," Exit"]})]})]}):(0,a.jsxs)("div",{className:"message-markdown markdown-body relative z-[1] text-foreground",children:["assistant"===e.role&&e.webDisplayContent&&(0,a.jsx)("div",{className:"message-prefix",children:(0,a.jsx)(n.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`})}),w.map(((e,t)=>{const s=e.match(j);return s&&s[1]?(0,a.jsx)(v,{content:s[1]},`think_${t}`):(0,a.jsx)("div",{className:"message-content",children:(0,a.jsx)(n.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:e})},`content_${t}`)}))]})})},w=({turns:e=[],isLoading:t=!1,onReload:s=()=>{},settingsMode:n=!1,onEditTurn:i})=>{const[d,u]=(0,r.useState)(-1),[m,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(""),{config:x}=(0,f.UK)(),v=(0,r.useRef)(null),b=(0,r.useRef)(null);(0,r.useLayoutEffect)((()=>{const e=b.current;e&&e.scrollHeight-e.scrollTop-e.clientHeight<200&&(e.scrollTop=e.scrollHeight)}),[e]);const w=e=>{navigator.clipboard.writeText(e)},j=(e,t)=>{h(e),g(t)},N=()=>{h(null),g("")},C=()=>{null!==m&&p.trim()&&i(m,p),N()};return(0,a.jsxs)("div",{ref:b,id:"messages",className:(0,c.cn)("flex flex-col flex-grow w-full overflow-y-auto pb-2 pt-2","no-scrollbar"),style:{background:"var(--bg)",opacity:n?0:1},children:[e.map(((t,r)=>t&&(0,a.jsxs)("div",{className:(0,c.cn)("flex items-start w-full mt-1 mb-1 px-2 relative","user"===t.role?"justify-start":"justify-end"),onMouseEnter:()=>u(r),onMouseLeave:()=>u(-1),children:["assistant"===t.role&&(0,a.jsxs)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 mr-0 pb-3 transition-opacity duration-100",d===r?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:[m!==r&&(0,a.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,a.jsx)(o.nxz,{className:"text-[var(--text)]"})}),r===e.length-1&&(0,a.jsx)(l.$,{"aria-label":"Reload",variant:"message-action",size:"xs",onClick:s,title:"Reload last prompt",children:(0,a.jsx)(o.jEl,{className:"text-[var(--text)]"})})]}),(0,a.jsx)(y,{turn:t,index:r,isEditing:m===r,editText:p,onStartEdit:j,onSetEditText:g,onSaveEdit:C,onCancelEdit:N}),"user"===t.role&&(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 ml-0 pb-1 transition-opacity duration-100",d===r?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:m!==r&&(0,a.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"sm",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,a.jsx)(o.nxz,{className:"text-[var(--text)]"})})})]},t.timestamp||`turn_${r}`))),(0,a.jsx)("div",{ref:v,style:{height:"1px"}})]})}},8698:(e,t,s)=>{s.d(t,{N:()=>o});var a=s(6540),r=s(6948);const o=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),s=(0,a.useRef)(0),o=[{host:"ollama",isEnabled:e=>!!e.ollamaUrl&&!0===e.ollamaConnected,getUrl:e=>`${e.ollamaUrl}/api/tags`,parseFn:(e,t)=>(e?.models??[]).map((e=>({...e,id:e.id??e.name,host:t}))),onFetchFail:(e,t)=>t({ollamaConnected:!1,ollamaUrl:""})}];return{fetchAllModels:(0,a.useCallback)((async()=>{const a=Date.now();if(a-s.current<3e4)return void console.log("[useUpdateModels] Model fetch throttled");s.current=a;const r=e;if(!r)return void console.warn("[useUpdateModels] Config not available, skipping fetch.");console.log("[useUpdateModels] Starting model fetch for all configured services...");const n=await Promise.allSettled(o.map((async e=>{if(!e.isEnabled(r))return{host:e.host,models:[],status:"disabled"};const s=e.getUrl(r);if(!s)return console.warn(`[useUpdateModels] Could not determine URL for host: ${e.host}`),{host:e.host,models:[],status:"error",error:"Invalid URL"};const a=e.getFetchOptions?e.getFetchOptions(r):{},o=await(async(e,t={})=>{try{const s=await fetch(e,t);return s.ok?await s.json():void console.error(`[fetchDataSilently] HTTP error! Status: ${s.status} for URL: ${e}`)}catch(t){return void console.error(`[fetchDataSilently] Fetch or JSON parse error for URL: ${e}`,t)}})(s,a);if(o){const t=e.parseFn(o,e.host);return{host:e.host,models:t,status:"success"}}return e.onFetchFail&&e.onFetchFail(r,t),{host:e.host,models:[],status:"error",error:"Fetch failed"}})));let i=[];n.forEach((e=>{"fulfilled"===e.status&&"success"===e.value.status&&i.push(...e.value.models)}));const l=r.models||[],c={};((e,t)=>{if(e.length!==t.length)return!0;const s=(e,t)=>e.id.localeCompare(t.id),a=[...e].sort(s),r=[...t].sort(s);return JSON.stringify(a)!==JSON.stringify(r)})(i,l)&&(console.log("[useUpdateModels] Aggregated models changed. Updating config."),c.models=i);const d=r.selectedModel,u=c.models||l,m=d&&u.some((e=>e.id===d))?d:u[0]?.id;(m!==d||c.models)&&(c.selectedModel=m),Object.keys(c).length>0?t(c):console.log("[useUpdateModels] No changes to models or selectedModel needed."),console.log("[useUpdateModels] Model fetch cycle complete.")}),[e,t,3e4,o])}}},8971:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(6540),r=s(2951),o=s(5431);const n=1e3;var i=s(1100);try{const e=chrome.runtime.getURL("pdf.worker.mjs");e?i.EA.workerSrc=e:console.error("Failed to get URL for pdf.worker.mjs. PDF parsing might fail.")}catch(e){console.error("Error setting pdf.js worker source:",e)}const l=(e,t,s,l,c,d,u,m,h,p,g)=>{const f=(0,a.useRef)(null),x=(0,a.useRef)(null),v=(e,t,s,a,r)=>{if(f.current===e||s||a||r){if(null===f.current&&null!==e&&(""===t&&s&&!a&&!r||a&&(t.includes("Operation cancelled by user")||t.includes("Streaming operation cancelled"))))return console.log(`[${e}] updateAssistantTurn: Signal received after operation already finalized. Preserving existing state.`),p(!1),void g("idle");d((o=>{if(0===o.length||"assistant"!==o[o.length-1].role){if(console.warn(`[${e}] updateAssistantTurn: No assistant turn found or last turn is not assistant.`),a){const e={role:"assistant",rawContent:`Error: ${t||"Unknown operation error"}`,status:"error",timestamp:Date.now()};return[...o,e]}return o}const n=o[o.length-1],i=!0===a?"error":!0===r?"cancelled":s?"complete":"streaming";let l;if(r){const e=n.rawContent||"";l=e+(e?" ":"")+t}else l=a?`Error: ${t||"Unknown stream/handler error"}`:t;return[...o.slice(0,-1),{...n,rawContent:l,status:i,timestamp:Date.now()}]})),(s||!0===a||!0===r)&&(console.log(`[${e}] updateAssistantTurn: Final state (Finished: ${s}, Error: ${a}, Cancelled: ${r}). Clearing guard and loading.`),p(!1),g(a||r?"idle":"done"),f.current===e&&(f.current=null,x.current&&(x.current=null)))}else console.log(`[${e}] updateAssistantTurn: Guard mismatch (current: ${f.current}), skipping non-final update.`)};return{onSend:async t=>{const a=Date.now();console.log(`[${a}] useSendMessage: onSend triggered.`);const l=t||"";if(!c)return console.log(`[${a}] useSendMessage: Bailing out: Missing config.`),void p(!1);if(!l||!c)return void console.log(`[${a}] useSendMessage: Bailing out: Missing message or config.`);null!==f.current&&(console.warn(`[${a}] useSendMessage: Another send operation (ID: ${f.current}) is already in progress. Aborting previous.`),x.current&&x.current.abort());const b=new AbortController;x.current=b,console.log(`[${a}] useSendMessage: Setting loading true.`),p(!0),m(""),h("");const y=c.chatMode||"chat";g("web"===y?"searching":"page"===y?"reading":"thinking"),f.current=a;const w=l.match(/(https?:\/\/[^\s]+)/g);let j="";if(w&&w.length>0){g("searching");try{j=(await Promise.all(w.map((e=>(0,r.hj)(e,b.signal))))).map(((e,t)=>`Content from [${w[t]}]:\n${e}`)).join("\n\n")}catch(e){j="[Error scraping one or more URLs]"}g("thinking")}const N={role:"user",status:"complete",rawContent:l,timestamp:Date.now()};d((e=>[...e,N])),u(""),console.log(`[${a}] useSendMessage: User turn added to state.`);const C={role:"assistant",rawContent:"",status:"streaming",timestamp:Date.now()+1};d((e=>[...e,C])),console.log(`[${a}] useSendMessage: Assistant placeholder turn added early.`);let S=l,k="",$="";const M=c?.models?.find((e=>e.id===c.selectedModel));if(!M)return console.error(`[${a}] useSendMessage: No current model found.`),void v(a,"Configuration error: No model selected.",!0,!0);const A=void 0;{console.log(`[${a}] useSendMessage: Optimizing query...`),g("thinking");const e=s.map((e=>({role:e.role,content:e.rawContent})));try{const t=await(0,r.GW)(l,c,M,A,b.signal,e);t&&t.trim()&&t!==l?(S=t,$=`**Optimized query:** "*${S}*"\n\n`,console.log(`[${a}] useSendMessage: Query optimized to: "${S}"`)):($=`**Original query:** "${S}"\n\n`,console.log(`[${a}] useSendMessage: Using original query: "${S}"`))}catch(e){console.error(`[${a}] Query optimization failed:`,e),$=`**Fallback query:** "${S}"\n\n`}}console.log(`[${a}] useSendMessage: Performing web search...`),g("searching");try{if(k=await(0,r.tE)(S,c,b.signal),g("thinking"),b.signal.aborted)return void console.log(`[${a}] Web search was aborted (signal check post-await).`)}catch(e){if(console.error(`[${a}] Web search failed:`,e),"AbortError"===e.name||b.signal.aborted)return void console.log(`[${a}] Web search aborted. onStop handler will finalize UI.`);{k="";const t=`Web Search Failed: ${e instanceof Error?e.message:String(e)}`;return g("idle"),void v(a,t,!0,!0,!1)}}console.log(`[${a}] useSendMessage: Web search completed. Length: ${k.length}`),$&&d((e=>e.map((t=>"assistant"===t.role&&e[e.length-1]===t&&"complete"!==t.status&&"error"!==t.status&&"cancelled"!==t.status?{...t,webDisplayContent:$}:t))));const E=S,T=1e3*(c?.webLimit||1),P=T&&"string"==typeof k?k.substring(0,T):k,z=128===c?.webLimit?k:P,L=s.map((e=>({content:e.rawContent||"",role:e.role}))).concat({role:"user",content:l});let _="";if("page"===c?.chatMode){let e="";console.log(`[${a}] useSendMessage: Preparing page content...`),g("reading");try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(t?.url&&!t.url.startsWith("chrome://")){const s=t.url,r=t.mimeType;if(s.toLowerCase().endsWith(".pdf")||r&&"application/pdf"===r){console.log(`[${a}] Detected PDF URL: ${s}. Attempting to extract text.`);try{e=await async function(e,t){try{console.log(`[${t||"PDF"}] Attempting to fetch PDF from URL: ${e}`);const s=await fetch(e);if(!s.ok)throw new Error(`Failed to fetch PDF: ${s.status} ${s.statusText}`);const a=await s.arrayBuffer();console.log(`[${t||"PDF"}] PDF fetched, size: ${a.byteLength} bytes. Parsing...`);const r=await i.YE({data:a}).promise;console.log(`[${t||"PDF"}] PDF parsed. Number of pages: ${r.numPages}`);let o="";for(let e=1;e<=r.numPages;e++){const s=await r.getPage(e);o+=(await s.getTextContent()).items.map((e=>"str"in e?e.str:"")).join(" ")+"\n\n",e%10!=0&&e!==r.numPages||console.log(`[${t||"PDF"}] Extracted text from page ${e}/${r.numPages}`)}return console.log(`[${t||"PDF"}] PDF text extraction complete. Total length: ${o.length}`),o.trim()}catch(s){throw console.error(`[${t||"PDF"}] Error extracting text from PDF (${e}):`,s),s}}(s,a),console.log(`[${a}] Successfully extracted text from PDF. Length: ${e.length}`)}catch(t){console.error(`[${a}] Failed to extract text from PDF ${s}:`,t),e=`Error extracting PDF content: ${t instanceof Error?t.message:"Unknown PDF error"}. Falling back.`}}else console.log(`[${a}] URL is not a PDF. Fetching from storage: ${s}`),e=await o.A.getItem("pagestring")||"",console.log(`[${a}] Retrieved page text content from storage. Length: ${e.length}`)}else console.log(`[${a}] Not fetching page content for URL: ${t?.url} (might be chrome:// or no active tab).`)}catch(t){console.error(`[${a}] Error getting active tab or initial page processing:`,t),e=`Error accessing page content: ${t instanceof Error?t.message:"Unknown error"}`}const t=1e3*(c?.contextLimit||1),s="string"==typeof e?e:"",r=t&&s?s.substring(0,t):s;_=128===c?.contextLimit?s:r,h(_||""),g("thinking"),console.log(`[${a}] Page content prepared for LLM. Length: ${_?.length}`)}else h("");const R=c?.personas?.[c?.persona]||"",O="page"===c?.chatMode&&_?`Use the following page content for context: ${_}`:"",I=z?`Refer to this web search summary: ${z}`:"",D=c?.useNote&&c.noteContent?`Refer to this note for context: ${c.noteContent}`:"";let F="";const U=c.userName?.trim(),q=c.userProfile?.trim();U&&"user"!==U.toLowerCase()&&""!==U?(F=`You are interacting with a user named "${U}".`,q&&(F+=` Their provided profile information is: "${q}".`)):q&&(F=`You are interacting with a user. Their provided profile information is: "${q}".`);const W=[];R&&W.push(R),F&&W.push(F),D&&W.push(D),O&&W.push(O),I&&W.push(I),j&&W.push(`Use the following scraped content from URLs in the user's message:\n${j}`);const B=W.join("\n\n").trim();console.log(`[${a}] useSendMessage: System prompt constructed. Persona: ${!!R}, UserCtx: ${!!F}, NoteCtx: ${!!D}, PageCtx: ${!!O}, WebCtx: ${!!I}, LinkCtx: ${!!j}`);try{if(g("thinking"),"high"===c?.computeLevel&&M)console.log(`[${a}] useSendMessage: Starting HIGH compute level.`),await(async(e,t,s,a,o,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into stages...",!1),d(),await new Promise((e=>setTimeout(e,n)));const u=`You are a planning agent. Given the original task: "${e}", break it down into the main sequential stages required to accomplish it. Output *only* a numbered list of stages. Example:\n1. First stage\n2. Second stage`,m=await(0,r.GW)(u,s,a,o,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("HighCompute - Raw L1 Decomposition Result:",m),i(`Monitoring: Generated Stages:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length)return i("Error: Failed to decompose task into stages. Falling back to direct query.",!0),"Error: Could not decompose task.";const p=[];for(let t=0;t<h.length;t++){const u=h[t];d(),i(`Processing Stage ${t+1}/${h.length}: ${u}...`,!1);const m=`You are a planning agent. Given the stage: "${u}", break it down into the specific sequential steps needed to complete it. Output *only* a numbered list of steps. If no further breakdown is needed, output "No breakdown needed."`;d(),await new Promise((e=>setTimeout(e,n)));const g=await(0,r.GW)(m,s,a,o,l,[],c);console.log(`HighCompute - Raw L2 Decomposition Result (Stage ${t+1}):`,g);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));i(`Monitoring: Generated Steps for Stage ${t+1}:\n${f.join("\n")||"[None, or direct solve]"}`,!1);let x="";if(0===f.length||g.includes("No breakdown needed")){i(`Solving Stage ${t+1} directly...`,!1),d();const c=`Complete the following stage based on the original task "${e}": "${u}"`;d(),await new Promise((e=>setTimeout(e,n))),x=await(0,r.GW)(c,s,a,o,l),console.log(`HighCompute - Raw Direct Solve Result (Stage ${t+1}):`,x),i(`Monitoring: Direct Solve Result for Stage ${t+1}:\n${x}`,!1)}else{const m=[],h=2;let p="";for(let c=0;c<f.length;c+=h){const g=f.slice(c,c+h),x=c/h+1;d(),i(`Solving Step Batch ${x} for Stage ${t+1}: ${g.join(", ")}...`,!1);const v=`You are an expert problem solver. Given the stage: "${u}" and the original task: "${e}", complete the following steps.  Consider the following accumulated context from previous steps: ${p}\n\n${g.map(((e,t)=>`${c+t+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the steps.`;d(),await new Promise((e=>setTimeout(e,n)));const b=await(0,r.GW)(v,s,a,o,l);console.log(`HighCompute - Raw Batch Results (Stage ${t+1}, Batch ${x}):`,b),i(`Monitoring: Raw Batch Results for Stage ${t+1}, Batch ${x}:\n${b}`,!1);const y=b.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`HighCompute - Parsed Batch Results (Stage ${t+1}, Batch ${x}):`,y),i(`Monitoring: Parsed Batch Results for Stage ${t+1}, Batch ${x}:\n${y.join("\n")||"[None]"}`,!1);for(let e=0;e<y.length;e++){const t=y[e];m.push(t),p+=`Step ${c+e+1}: ${t}\n`}}i(`Synthesizing results for Stage ${t+1}...`,!1),d(),await new Promise((e=>setTimeout(e,n)));const g=`Synthesize the results of the following steps for stage "${u}" into a coherent paragraph:\n\n${m.map(((e,t)=>`Step ${t+1} Result:\n${e}`)).join("\n\n")}`;x=await(0,r.GW)(g,s,a,o,l,[],c),console.log(`HighCompute - Raw Stage Synthesis Result (Stage ${t+1}):`,x),i(`Monitoring: Synthesized Result for Stage ${t+1}:\n${x}`,!1)}p.push(x),i(`Monitoring: Accumulated Stage Results so far:\n${p.map(((e,t)=>`Stage ${t+1}: ${e}`)).join("\n---\n")}`,!1)}i("Synthesizing final answer...",!1),d();const g=`Based on the results of the following stages, provide a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Stage ${t+1} (${h[t]}):\n${e}`)).join("\n\n")}`;i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1),console.log("HighCompute - Final Synthesis Prompt:",g),d(),await new Promise((e=>setTimeout(e,n)));const f=await(0,r.GW)(g,s,a,o,l,[],c);console.log("HighCompute - Raw Final Synthesis Result:",f);const x="**High Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Stage ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(E,0,c,M,A,((e,t)=>v(a,e,Boolean(t))),b.signal),console.log(`[${a}] useSendMessage: HIGH compute level finished.`);else if("medium"===c?.computeLevel&&M)console.log(`[${a}] useSendMessage: Starting MEDIUM compute level.`),await(async(e,t,s,a,o,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into subtasks...",!1),d(),await new Promise((e=>setTimeout(e,n)));const u=`You are a planning agent. Given the task: "${e}", break it down into logical subtasks needed to accomplish it. Output *only* a numbered list of subtasks.`,m=await(0,r.GW)(u,s,a,o,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("MediumCompute - Raw Decomposition Result:",m),i(`Monitoring: Generated Subtasks:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length){i("Warning: Failed to decompose into subtasks. Attempting direct query.",!1),d(),await new Promise((e=>setTimeout(e,n)));const t=await(0,r.GW)(e,s,a,o,l);return i(t,!0),t}const p=[];for(let t=0;t<h.length;t+=2){const c=h.slice(t,t+2),u=t/2+1;d(),i(`Solving Subtask Batch ${u}: ${c.join(", ")}...`,!1);const m=`You are an expert problem solver. Given the task: "${e}", complete the following subtasks:\n\n${c.map(((e,s)=>`${t+s+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the subtasks.`;d(),await new Promise((e=>setTimeout(e,n)));const g=await(0,r.GW)(m,s,a,o,l);console.log(`MediumCompute - Raw Batch Results (Batch ${u}):`,g),i(`Monitoring: Raw Batch Results for Batch ${u}:\n${g}`,!1);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`MediumCompute - Parsed Batch Results (Batch ${u}):`,f),i(`Monitoring: Parsed Batch Results for Batch ${u}:\n${f.join("\n")||"[None]"}`,!1);for(let e=0;e<f.length;e++)p.push(f[e])}i("Synthesizing final answer...",!1),d(),await new Promise((e=>setTimeout(e,n)));const g=`Synthesize the results of the following subtasks into a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Subtask ${t+1} Result:\n${e}`)).join("\n\n")}`;console.log("MediumCompute - Final Synthesis Prompt:",g),i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1);const f=await(0,r.GW)(g,s,a,o,l,[],c);console.log("MediumCompute - Raw Final Synthesis Result:",f);const x="**Medium Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Subtask ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(E,0,c,M,A,((e,t)=>v(a,e,Boolean(t))),b.signal),console.log(`[${a}] useSendMessage: MEDIUM compute level finished.`);else{console.log(`[${a}] useSendMessage: Starting standard streaming.`);const e={stream:!0},t={ollama:`${c?.ollamaUrl||""}/api/chat`}[M.host||""];if(!t)return void v(a,`Configuration error: Could not determine API URL for host '${M.host}'.`,!0,!0);const s=[];""!==B.trim()&&s.push({role:"system",content:B}),s.push(...L),console.log(`[${a}] useSendMessage: Sending chat request to ${t} with system prompt: "${B}"`),await(0,r.hL)(t,{...e,model:c?.selectedModel||"",messages:s,temperature:c?.temperature??.7,max_tokens:c?.maxTokens??32048,top_p:c?.topP??1,presence_penalty:c?.presencepenalty??0},((e,t,s)=>{v(a,e,Boolean(t),Boolean(s)),(t||s)&&console.log(`[${a}] fetchDataAsStream Callback: Stream finished/errored.`)}),A,M.host||"",b.signal),console.log(`[${a}] useSendMessage: fetchDataAsStream call INITIATED.`)}}catch(t){if(b.signal.aborted)console.log(`[${a}] Send operation was aborted. 'onStop' handler is responsible for UI updates.`),e&&p(!1),g("idle"),f.current===a&&(f.current=null),x.current&&x.current.signal===b.signal&&(x.current=null);else{console.error(`[${a}] useSendMessage: Error during send operation:`,t);const e=t instanceof Error?t.message:String(t);v(a,e,!0,!0)}}console.log(`[${a}] useSendMessage: onSend processing logic completed.`)},onStop:()=>{const e=f.current;null!==e?(console.log(`[${e}] useSendMessage: onStop triggered.`),x.current&&(x.current.abort(),x.current=null),v(e,"[Operation cancelled by user]",!0,!1,!0)):(console.log("[No CallID] useSendMessage: onStop triggered but no operation in progress."),p(!1),g("idle"))}}}},9018:(e,t,s)=>{s.d(t,{bq:()=>p,eb:()=>f,gC:()=>g,l6:()=>m,yv:()=>h});var a=s(4848),r=s(6540),o=s(854),n=s(5107),i=s(5773),l=s(2102),c=s(5284);const d={default:"bg-transparent data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs data-[size=sm]:h-8",settingsPanel:(0,c.cn)("text-[var(--text)] rounded-xl shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--bg)] border border-[var(--text)]/20","backdrop-blur-sm","h-8"),settings:(0,c.cn)("text-[var(--text)] rounded-md shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--bg)] border border-[var(--text)]/20","backdrop-blur-sm","h-8")},u={default:"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",settingsPanel:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto","bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]/10","rounded-xl shadow-lg backdrop-blur-sm")};function m({...e}){return(0,a.jsx)(o.bL,{"data-slot":"select",...e})}function h({...e}){return(0,a.jsx)(o.WT,{"data-slot":"select-value",...e})}const p=r.forwardRef((({className:e,size:t="default",variant:s="default",children:r,...i},l)=>(0,a.jsxs)(o.l9,{ref:l,"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("flex w-fit items-center justify-between gap-2 rounded-md border px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",d[s],e),...i,children:[r,(0,a.jsx)(o.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})));p.displayName=o.l9.displayName;const g=r.forwardRef((({className:e,children:t,position:s="popper",variant:r="default",...n},i)=>(0,a.jsx)(o.ZL,{children:(0,a.jsxs)(o.UC,{ref:i,"data-slot":"select-content",className:(0,c.cn)(u[r],"default"===r&&"popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(x,{}),(0,a.jsx)(o.LM,{className:(0,c.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(v,{})]})})));function f({className:e,children:t,focusVariant:s="default",...r}){return(0,a.jsxs)(o.q7,{"data-slot":"select-item",className:(0,c.cn)("[&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2","activeTheme"===s?"text-[var(--text)] hover:bg-[var(--active)]/10 focus:bg-[var(--active)]/20 focus:text-[var(--text)] data-[highlighted]:bg-[var(--active)]/15":"text-popover-foreground focus:bg-accent focus:text-accent-foreground",e),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(o.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(o.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(o.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function v({className:e,...t}){return(0,a.jsx)(o.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}g.displayName=o.UC.displayName},9696:(e,t,s)=>{s.d(t,{T:()=>n});var a=s(4848),r=(s(6540),s(1663)),o=s(5284);function n({className:e,autosize:t=!1,minRows:s,maxRows:n,style:i,...l}){return t?(0,a.jsx)(r.A,{minRows:s,maxRows:n,style:i,className:(0,o.cn)("flex w-full bg-transparent placeholder:text-muted-foreground","focus-visible:border-ring focus-visible:ring-ring/50","field-sizing-content text-sm md:text-sm transition-[color,box-shadow] outline-none focus-visible:ring-[3px]","disabled:cursor-not-allowed disabled:opacity-50","thin-scrollbar",e),...l}):(0,a.jsx)("textarea",{"data-slot":"textarea-default",className:(0,o.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...l})}},9828:(e,t,s)=>{s.a(e,(async(e,a)=>{try{s.d(t,{A:()=>O});var r=s(4848),o=s(6540),n=s(888),i=s(3790),l=s.n(i),c=s(5066),d=s(1735),u=s(9197),m=s(2090),h=s(3885),p=s(5284),g=s(9853),f=s(8971),x=s(8698),v=s(523),b=s(8473),y=s(6948),w=s(4156),j=s(3828),N=s(8639),C=s(7660),S=s(2823),k=s(5431),$=s(6174),M=s(5095),A=s(1979),E=e([C]);function T(){let e="",t="",s="",a="",r="",o="",n="";try{e=document.title||"";const i=5e6;if(document.body,document.body&&document.body.innerHTML.length>i){console.warn(`[ChromePanion Bridge] Document body is very large (${document.body.innerHTML.length} chars). Attempting to use a cloned, simplified version for text extraction to improve performance/stability.`);const e=document.body.cloneNode(!0);e.querySelectorAll("script, style, noscript, iframe, embed, object").forEach((e=>e.remove())),t=(e.textContent||"").replace(/\s\s+/g," ").trim(),s=document.body.innerHTML.replace(/\s\s+/g," ")}else document.body?(t=(document.body.innerText||"").replace(/\s\s+/g," ").trim(),s=(document.body.innerHTML||"").replace(/\s\s+/g," ")):console.warn("[ChromePanion Bridge] document.body is not available.");a=Array.from(document.images).map((e=>e.alt)).filter((e=>e&&e.trim().length>0)).join(". "),r=Array.from(document.querySelectorAll("table")).map((e=>(e.innerText||"").replace(/\s\s+/g," "))).join("\n");const l=document.querySelector('meta[name="description"]');o=l&&l.getAttribute("content")||"";const c=document.querySelector('meta[name="keywords"]');n=c&&c.getAttribute("content")||""}catch(e){console.error("[ChromePanion Bridge] Error during content extraction:",e);let t="Unknown extraction error";return e instanceof Error?t=e.message:"string"==typeof e&&(t=e),JSON.stringify({error:`Extraction failed: ${t}`,title:document.title||"Error extracting title",text:"",html:"",altTexts:"",tableData:"",meta:{description:"",keywords:""}})}const i=1e7;let l={title:e,text:t,html:s,altTexts:a,tableData:r,meta:{description:o,keywords:n}};if(JSON.stringify(l).length>i){console.warn("[ChromePanion Bridge] Total extracted content is very large. Attempting to truncate.");const e=i-JSON.stringify({...l,text:"",html:""}).length;let t=e;l.text.length>.6*t&&(l.text=l.text.substring(0,Math.floor(.6*t))+"... (truncated)"),t=e-l.text.length,l.html.length>.8*t&&(l.html=l.html.substring(0,Math.floor(.8*t))+"... (truncated)"),console.warn("[ChromePanion Bridge] Content truncated. Final approx length:",JSON.stringify(l).length)}return JSON.stringify(l)}async function P(){const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(!e?.id||e.url?.startsWith("chrome://")||e.url?.startsWith("chrome-extension://")||e.url?.startsWith("about:"))return k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),void k.A.deleteItem("tabledata");k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata");try{const t=await chrome.scripting.executeScript({target:{tabId:e.id},func:T});if(!t||!Array.isArray(t)||0===t.length||!t[0]||"string"!=typeof t[0].result)return void console.error("[ChromePanion:] Bridge function execution returned invalid or unexpected results structure:",t);const s=t[0].result;let a;try{a=JSON.parse(s)}catch(e){return void console.error("[ChromePanion:] Failed to parse JSON result from bridge:",e,"Raw result string:",s)}if(a.error)return void console.error("[ChromePanion:] Bridge function reported an error:",a.error,"Title:",a.title);try{k.A.setItem("pagestring",a?.text??""),k.A.setItem("pagehtml",a?.html??""),k.A.setItem("alttexts",a?.altTexts??""),k.A.setItem("tabledata",a?.tableData??"")}catch(e){console.error("[ChromePanion:] Storage error after successful extraction:",e),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")}}catch(e){console.error("[ChromePanion:] Bridge function execution failed:",e),e instanceof Error&&(e.message.includes('Cannot access contents of url "chrome://')||e.message.includes("Cannot access a chrome extension URL")||e.message.includes('Cannot access contents of url "about:'))&&console.warn("[ChromePanion:] Cannot access restricted URL.")}}C=(E.then?(await E)():E)[0];const z=()=>`chat_${Math.random().toString(16).slice(2)}`,L=({children:e,onClick:t})=>(0,r.jsx)("div",{className:(0,p.cn)("bg-[var(--active)] border border-[var(--text)] rounded-[16px] text-[var(--text)]","cursor-pointer flex items-center justify-center","text-md font-extrabold p-0.5 place-items-center relative text-center","w-16 flex-shrink-0","transition-colors duration-200 ease-in-out","hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,children:e}),_=[{id:"Google",icon:u.DSS,label:"Google Search"}],R=({children:e,onClick:t,isActive:s,title:a})=>(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)("div",{className:(0,p.cn)("border rounded-lg text-[var(--text)]","cursor-pointer flex items-center justify-center","p-2 place-items-center relative","w-8 h-8 flex-shrink-0","transition-colors duration-200 ease-in-out",s?"bg-[var(--active)] text-[var(--text)] border-[var(--active)] hover:brightness-95":"bg-transparent border-[var(--text)]/50 hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,"aria-label":a,children:e})}),(0,r.jsx)(h.ZI,{side:"top",className:"bg-[var(--active)]/80 text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:a})})]}),O=()=>{const[e,t]=(0,o.useState)([]),[s,a]=(0,o.useState)(""),[i,u]=(0,o.useState)(z()),[E,T]=(0,o.useState)(""),[O,I]=(0,o.useState)(""),[D,F]=(0,o.useState)(!1),[U,q]=(0,o.useState)(!1),[W,B]=(0,o.useState)(!1),{config:G,updateConfig:H}=(0,y.UK)(),[V,Y]=(0,o.useState)(!1),[J,K]=(0,o.useState)({id:null,url:""}),Z=(0,o.useRef)(null),Q=(0,o.useRef)({id:null,url:""}),[X,ee]=(0,o.useState)(!1),[te,se]=(0,o.useState)(!1),[ae,re]=(0,o.useState)("idle"),[oe,ne]=(0,o.useState)(!1);(0,o.useRef)(null),(0,o.useEffect)((()=>{const e=new ResizeObserver((()=>{Z.current&&(Z.current.style.minHeight="100dvh",requestAnimationFrame((()=>{Z.current&&(Z.current.style.minHeight="")})))}));return Z.current&&e.observe(Z.current),()=>e.disconnect()}),[]),(0,o.useEffect)((()=>{if("page"!==G?.chatMode)return;const e=async()=>{const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(e?.id&&e.url)return e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||e.url.startsWith("about:")?(Q.current.id===e.id&&Q.current.url===e.url||(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")),Q.current={id:e.id,url:e.url},void K({id:e.id,url:e.url})):void(e.id===Q.current.id&&e.url===Q.current.url||(Q.current={id:e.id,url:e.url},K({id:e.id,url:e.url}),await P()))};e();const t=t=>{chrome.tabs.get(t.tabId,(t=>{chrome.runtime.lastError?console.warn(`[ChromePanion ] Error getting tab info on activation: ${chrome.runtime.lastError.message}`):e()}))},s=(t,s,a)=>{a.active&&("complete"===s.status||s.url&&"complete"===a.status)&&e()};return chrome.tabs.onActivated.addListener(t),chrome.tabs.onUpdated.addListener(s),()=>{chrome.tabs.onActivated.removeListener(t),chrome.tabs.onUpdated.removeListener(s),Q.current={id:null,url:""}}}),[G?.chatMode]),(0,o.useEffect)((()=>{const e=e=>{};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[G?.chatMode,H,U,W]),(0,o.useEffect)((()=>{const e=(e,t,s)=>"ACTIVATE_NOTE_SYSTEM_VIEW"===e.type&&(console.log("[ChromePanion.tsx] Received ACTIVATE_NOTE_SYSTEM_VIEW. Switching to Note System mode."),q(!1),B(!1),Y(!0),s({status:"ACTIVATING_NOTE_SYSTEM_VIEW_ACK"}),!0);return chrome.runtime.onMessage.addListener(e),()=>{chrome.runtime.onMessage.removeListener(e)}}),[q,B,Y]);const{appendToNote:ie}=(0,M.e)();(0,o.useEffect)((()=>{const e=chrome.runtime.connect({name:$.A.SidePanelPort}),t=e=>{"ADD_SELECTION_TO_NOTE"===e.type&&e.payload&&ie(e.payload)};return e.onMessage.addListener(t),e.postMessage({type:"init"}),()=>{e.onMessage.removeListener(t),e.disconnect()}}),[ie]);const{chatTitle:le,setChatTitle:ce}=(0,g.S)(D,e,s),{onSend:de,onStop:ue}=(0,f.A)(D,s,e,E,G,t,a,T,I,F,re);(0,x.N)();const me=()=>{t([]),I(""),T(""),F(!1),H({chatMode:"web",computeLevel:"low"}),re("idle"),a(""),ce(""),u(z()),B(!1),q(!1),Y(!1),Z.current&&(Z.current.scrollTop=0)},he=async()=>{try{const t=(await l().keys()).filter((e=>e.startsWith("chat_")));if(0===t.length&&0===e.length)return;await Promise.all(t.map((e=>l().removeItem(e)))),n.oR.success("Deleted all chats"),me()}catch(e){console.error("[ChromePanion] Error deleting all chats:",e),n.oR.error("Failed to delete chats")}};(0,o.useEffect)((()=>{if(e.length>0&&!W&&!U&&!V){const t={id:i,title:le||`Chat ${new Date(Date.now()).toLocaleString()}`,turns:e,last_updated:Date.now(),model:G?.selectedModel,chatMode:G?.chatMode,webMode:"web"===G?.chatMode?G.webMode:void 0,useNoteActive:G?.useNote,noteContentUsed:G?.useNote?G.noteContent:void 0};l().setItem(i,t).catch((e=>{console.error(`[ChromePanion ] Error saving chat ${i}:`,e)}))}}),[i,e,le,G?.selectedModel,G?.chatMode,G?.webMode,G?.useNote,G?.noteContent,W,U]),(0,o.useEffect)((()=>{if("done"===ae||"idle"===ae){const e=setTimeout((()=>{re("idle")}),1500);return()=>clearTimeout(e)}}),[ae]),(0,o.useEffect)((()=>{let e=!1;return(async()=>{if(!e){me();try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});!e&&t?.id&&t.url?(K({id:t.id,url:t.url}),(t.url.startsWith("chrome://")||t.url.startsWith("chrome-extension://")||t.url.startsWith("about:"))&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})):e||(Q.current={id:null,url:""},K({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}catch(t){e||(console.error("[ChromePanion - Revised] Error during panel open tab check:",t),Q.current={id:null,url:""},K({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}}})(),()=>{e=!0,k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),me(),Q.current={id:null,url:""}}}),[]);const pe=(0,o.useCallback)((()=>{ne(!1)}),[]);return(0,r.jsx)(h.Bc,{delayDuration:300,children:(0,r.jsxs)("div",{ref:Z,className:(0,p.cn)("w-full h-dvh p-0 overflow-hidden","flex flex-col bg-[var(--bg)]"),children:[(0,r.jsx)(w.Y,{chatTitle:le,deleteAll:he,downloadImage:()=>(0,C.GV)(e),downloadJson:()=>(0,C.xD)(e),downloadText:()=>(0,C.mR)(e),downloadMarkdown:()=>(0,C.ii)(e),historyMode:W,reset:me,setHistoryMode:B,setSettingsMode:q,settingsMode:U,noteSystemMode:V,onAddNewNoteRequest:V?()=>ne(!0):void 0,setNoteSystemMode:Y,chatMode:G?.chatMode||"chat",chatStatus:ae}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 no-scrollbar overflow-y-auto relative",children:[U&&(0,r.jsx)(S.w,{}),!U&&W&&!V&&(0,r.jsx)(b.D,{className:"flex-1 w-full min-h-0",loadChat:e=>{ce(e.title||""),t(e.turns),u(e.id),B(!1),re("idle"),q(!1);const s={useNote:e.useNoteActive??!1,noteContent:e.noteContentUsed||""};H(s),"page"!==s.chatMode&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})},onDeleteAll:he}),!U&&!W&&V&&(0,r.jsx)(A.z,{triggerOpenCreateModal:oe,onModalOpened:pe}),!U&&!W&&!V&&(0,r.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 relative",children:[(0,r.jsx)(N.B,{isLoading:D,turns:e,settingsMode:U,onReload:()=>{t((e=>{if(e.length<2)return e;const t=e[e.length-1],s=e[e.length-2];return"assistant"===t.role&&"user"===s.role?(a(s.rawContent),e.slice(0,-2)):e})),F(!1),re("idle")},onEditTurn:(e,s)=>{t((t=>{const a=[...t];return a[e]&&(a[e]={...a[e],rawContent:s}),a}))}}),0===e.length&&!G?.chatMode&&(0,r.jsxs)("div",{className:"fixed bottom-20 left-8 flex flex-col gap-2 z-[5]",children:[(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{asChild:!0,children:(0,r.jsx)(m.$,{"aria-label":"Cycle compute level",variant:"ghost",size:"icon",onClick:()=>{const e=G.computeLevel;H({computeLevel:"low"===e?"medium":"medium"===e?"high":"low"})},className:(0,p.cn)("hover:bg-secondary/70","high"===G.computeLevel?"text-red-600":"medium"===G.computeLevel?"text-orange-300":"text-[var(--text)]"),children:(0,r.jsx)(d.cfR,{})})}),(0,r.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)] max-w-80",children:(0,r.jsx)("p",{children:`Compute Level: ${G.computeLevel?.toUpperCase()}. Click to change. [Warning]: beta feature and resource costly.`})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{asChild:!0,children:(0,r.jsx)(m.$,{"aria-label":"Add Web Search Results to LLM Context",variant:"ghost",size:"icon",onClick:()=>{H({chatMode:"web",webMode:G.webMode||_[0].id})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,r.jsx)(c.pqQ,{})})}),(0,r.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,r.jsx)("p",{children:"Add Web Search Results to LLM Context"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{asChild:!0,children:(0,r.jsx)(m.$,{"aria-label":"Add Current Web Page to LLM Context",variant:"ghost",size:"icon",onClick:()=>{H({chatMode:"page"})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,r.jsx)(c.RGv,{})})}),(0,r.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,r.jsx)("p",{children:"Add Current Web Page to LLM Context"})})]})]}),"page"===G?.chatMode&&(0,r.jsx)("div",{className:(0,p.cn)("fixed bottom-16 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-8 z-[2]","transition-all duration-200 ease-in-out",X?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>ee(!0),onMouseLeave:()=>ee(!1),children:(0,r.jsxs)("div",{className:"flex items-center space-x-6 max-w-full overflow-x-auto px-0",children:[(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(L,{onClick:()=>de("Provide your summary."),children:"TLDR"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Quick Summary"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(L,{onClick:()=>de("Extract all key figures, names, locations, and dates mentioned on this page and list them."),children:"Facts"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Numbers, events, names"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(L,{onClick:()=>de("Find positive developments, achievements, or opportunities mentioned on this page."),children:"Yay!"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Good news"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(L,{onClick:()=>de("Find concerning issues, risks, or criticisms mentioned on this page."),children:"Oops"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Bad news"})})]})]})}),"web"===G?.chatMode&&(0,r.jsx)("div",{className:(0,p.cn)("fixed bottom-14 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-10 z-[2]","transition-all duration-200 ease-in-out",te?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>se(!0),onMouseLeave:()=>se(!1),children:(0,r.jsx)("div",{className:"flex items-center space-x-4 max-w-full overflow-x-auto px-4 py-1",children:_.map((e=>(0,r.jsx)(R,{onClick:()=>{H({webMode:e.id,chatMode:"web"})},isActive:G.webMode===e.id,title:e.label,children:(0,r.jsx)(e.icon,{size:18})},e.id)))})})]})]}),!U&&!W&&!V&&(0,r.jsx)("div",{className:"p-2 relative z-[10]",children:(0,r.jsx)(j.p,{isLoading:D,message:s,setMessage:a,onSend:()=>de(s),onStopRequest:ue})}),G?.backgroundImage?(0,r.jsx)(v.V,{}):null,(0,r.jsx)(n.l$,{containerStyle:{borderRadius:16,bottom:"60px"},toastOptions:{duration:2e3,position:"bottom-center",style:{background:"var(--bg)",color:"var(--text)",fontSize:"1rem",border:"1px solid var(--text)",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},success:{duration:2e3,style:{background:"var(--bg)",color:"var(--text)",fontSize:"1.25rem"}}}})]})})};a()}catch(I){a(I)}}))},9853:(e,t,s)=>{s.d(t,{S:()=>i});var a=s(6540),r=s(6948),o=s(2951);const n=e=>{const t=e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/"/g,"").replace(/#/g,"").trim();return t&&t.split(/\s+/).slice(0,4).join(" ")||"New Chat"},i=(e,t,s)=>{const[i,l]=(0,a.useState)(""),{config:c}=(0,r.UK)(),d=(0,a.useRef)(null);return(0,a.useEffect)((()=>{if(!e&&t.length>=2&&!i&&c?.generateTitle){d.current&&d.current.abort(),d.current=new AbortController;const e=d.current.signal,s=c?.models?.find((e=>e.id===c.selectedModel));if(!s)return;const a=[...t.slice(0,2).map((e=>({content:e.rawContent||"",role:e.role}))),{role:"user",content:"Create a short 2-4 word title for this chat. Keep it concise, just give me the best one, just one. No explanations or thinking steps needed."}],r=(()=>{const e={body:{model:s.id,messages:a,stream:!["ollama","lmStudio"].includes(s.host||"")},headers:{}};if("ollama"===s.host)return{...e,url:`${c.ollamaUrl}/api/chat`}})();if(!r)return;const i=t=>{e.aborted?console.log("Title generation aborted."):console.error("Title generation failed:",t)};if(["ollama"].includes(s.host||""))fetch(r.url,{method:"POST",headers:{"Content-Type":"application/json",...r.headers},body:JSON.stringify(r.body),signal:e}).then((e=>e.json())).then((e=>{const t=e.choices?.[0]?.message?.content||"",s=n(t);s&&(console.log("Setting chat title (local):",s),l(s))})).catch(i);else{let t="";(0,o.hL)(r.url,r.body,((s,a)=>{if(t=s,e.aborted)console.log("Title streaming aborted during callback.");else if(a){const e=n(t);e&&(console.log("Setting chat title (streaming):",e),l(e))}}),r.headers,s.host||"",e)}}return()=>{d.current&&(d.current.abort(),d.current=null)}}),[e,t,s,c,i]),{chatTitle:i,setChatTitle:l}}}},l={};function c(e){var t=l[e];if(void 0!==t)return t.exports;var s=l[e]={exports:{}};return i[e].call(s.exports,s,s.exports,c),s.exports}c.m=i,e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",s="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",a=e=>{e&&e.d<1&&(e.d=1,e.forEach((e=>e.r--)),e.forEach((e=>e.r--?e.r++:e())))},c.a=(r,o,n)=>{var i;n&&((i=[]).d=-1);var l,c,d,u=new Set,m=r.exports,h=new Promise(((e,t)=>{d=t,c=e}));h[t]=m,h[e]=e=>(i&&e(i),u.forEach(e),h.catch((e=>{}))),r.exports=h,o((r=>{var o;l=(r=>r.map((r=>{if(null!==r&&"object"==typeof r){if(r[e])return r;if(r.then){var o=[];o.d=0,r.then((e=>{n[t]=e,a(o)}),(e=>{n[s]=e,a(o)}));var n={};return n[e]=e=>e(o),n}}var i={};return i[e]=e=>{},i[t]=r,i})))(r);var n=()=>l.map((e=>{if(e[s])throw e[s];return e[t]})),c=new Promise((t=>{(o=()=>t(n)).r=0;var s=e=>e!==i&&!u.has(e)&&(u.add(e),e&&!e.d&&(o.r++,e.push(o)));l.map((t=>t[e](s)))}));return o.r?c:n()}),(e=>(e?d(h[s]=e):c(m),a(i)))),i&&i.d<0&&(i.d=0)},r=[],c.O=(e,t,s,a)=>{if(!t){var o=1/0;for(d=0;d<r.length;d++){for(var[t,s,a]=r[d],n=!0,i=0;i<t.length;i++)(!1&a||o>=a)&&Object.keys(c.O).every((e=>c.O[e](t[i])))?t.splice(i--,1):(n=!1,a<o&&(o=a));if(n){r.splice(d--,1);var l=s();void 0!==l&&(e=l)}}return e}a=a||0;for(var d=r.length;d>0&&r[d-1][2]>a;d--)r[d]=r[d-1];r[d]=[t,s,a]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var s=Object.create(null);c.r(s);var a={};o=o||[null,n({}),n([]),n(n)];for(var r=2&t&&e;"object"==typeof r&&!~o.indexOf(r);r=n(r))Object.getOwnPropertyNames(r).forEach((t=>a[t]=()=>e[t]));return a.default=()=>e,c.d(s,a),s},c.d=(e,t)=>{for(var s in t)c.o(t,s)&&!c.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=524,(()=>{var e={524:0};c.O.j=t=>0===e[t];var t=(t,s)=>{var a,r,[o,n,i]=s,l=0;if(o.some((t=>0!==e[t]))){for(a in n)c.o(n,a)&&(c.m[a]=n[a]);if(i)var d=i(c)}for(t&&t(s);l<o.length;l++)r=o[l],c.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return c.O(d)},s=globalThis.webpackChunkchromepanion=globalThis.webpackChunkchromepanion||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))})(),c.nc=void 0;var d=c.O(void 0,[465],(()=>c(3003)));d=c.O(d)})();