(()=>{var e={140:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DIFF_STATUS_UPDATED=t.DIFF_STATUS_REMOVED=t.DIFF_STATUS_KEYS_UPDATED=t.DIFF_STATUS_ARRAY_UPDATED=void 0,t.DIFF_STATUS_UPDATED="updated",t.DIFF_STATUS_REMOVED="removed",t.DIFF_STATUS_KEYS_UPDATED="updated_keys",t.DIFF_STATUS_ARRAY_UPDATED="updated_array"},368:e=>{var t=9007199254740991,r=/^(?:0|[1-9]\d*)$/,n=Object.prototype,o=n.hasOwnProperty,i=n.toString,a=n.propertyIsEnumerable,c=Math.max;function s(e,t,r){var n=e[t];o.call(e,t)&&l(n,r)&&(void 0!==r||t in e)||(e[t]=r)}function u(e,n){return!!(n=null==n?t:n)&&("number"==typeof e||r.test(e))&&e>-1&&e%1==0&&e<n}function l(e,t){return e===t||e!=e&&t!=t}var f=Array.isArray;function d(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=t}(e.length)&&!function(e){var t=p(e)?i.call(e):"";return"[object Function]"==t||"[object GeneratorFunction]"==t}(e)}function p(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}var h,y,g,m=(h=function(e,t){!function(e,t,r){r||(r={});for(var n=-1,o=t.length;++n<o;){var i=t[n];s(r,i,e[i])}}(t,function(e){return d(e)?function(e,t){var r=f(e)||function(e){return function(e){return function(e){return!!e&&"object"==typeof e}(e)&&d(e)}(e)&&o.call(e,"callee")&&(!a.call(e,"callee")||"[object Arguments]"==i.call(e))}(e)?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],n=r.length,c=!!n;for(var s in e)!t&&!o.call(e,s)||c&&("length"==s||u(s,n))||r.push(s);return r}(e,!0):function(e){if(!p(e))return function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}(e);var t,r,i=(r=(t=e)&&t.constructor,t===("function"==typeof r&&r.prototype||n)),a=[];for(var c in e)("constructor"!=c||!i&&o.call(e,c))&&a.push(c);return a}(e)}(t),e)},y=function(e,t){var r=-1,n=t.length,o=n>1?t[n-1]:void 0,i=n>2?t[2]:void 0;for(o=h.length>3&&"function"==typeof o?(n--,o):void 0,i&&function(e,t,r){if(!p(r))return!1;var n=typeof t;return!!("number"==n?d(r)&&u(t,r.length):"string"==n&&t in r)&&l(r[t],e)}(t[0],t[1],i)&&(o=n<3?void 0:o,n=1),e=Object(e);++r<n;){var a=t[r];a&&h(e,a)}return e},g=c(void 0===g?y.length-1:g,0),function(){for(var e=arguments,t=-1,r=c(e.length-g,0),n=Array(r);++t<r;)n[t]=e[g+t];t=-1;for(var o=Array(g+1);++t<g;)o[t]=e[t];return o[g]=n,function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}(y,this,o)});e.exports=m},1732:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=s(r(368)),o=r(9529),i=r(7575),a=s(r(3807)),c=r(6183);function s(e){return e&&e.__esModule?e:{default:e}}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,f(n.key),n)}}function f(e){var t=function(e){if("object"!=u(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==u(t)?t:t+""}var d="\nLooks like there is an error in the background page. You might want to inspect your background page for more details.\n",p={channelName:o.DEFAULT_CHANNEL_NAME,state:{},serializer:i.noop,deserializer:i.noop,patchStrategy:a.default},h=function(){return e=function e(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,n=r.channelName,a=void 0===n?p.channelName:n,s=r.state,u=void 0===s?p.state:s,l=r.serializer,f=void 0===l?p.serializer:l,d=r.deserializer,h=void 0===d?p.deserializer:d,y=r.patchStrategy,g=void 0===y?p.patchStrategy:y;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!a)throw new Error("channelName is required in options");if("function"!=typeof f)throw new Error("serializer must be a function");if("function"!=typeof h)throw new Error("deserializer must be a function");if("function"!=typeof g)throw new Error("patchStrategy must be one of the included patching strategies or a custom patching function");this.channelName=a,this.readyResolved=!1,this.readyPromise=new Promise((function(e){return t.readyResolve=e})),this.browserAPI=(0,c.getBrowserAPI)(),this.initializeStore=this.initializeStore.bind(this),this.browserAPI.runtime.sendMessage({type:o.FETCH_STATE_TYPE,channelName:a},void 0,this.initializeStore),this.deserializer=h,this.serializedPortListener=(0,i.withDeserializer)(h)((function(){var e;return(e=t.browserAPI.runtime.onMessage).addListener.apply(e,arguments)})),this.serializedMessageSender=(0,i.withSerializer)(f)((function(){var e;return(e=t.browserAPI.runtime).sendMessage.apply(e,arguments)})),this.listeners=[],this.state=u,this.patchStrategy=g,this.serializedPortListener((function(e){if(e&&e.channelName===t.channelName)switch(e.type){case o.STATE_TYPE:t.replaceState(e.payload),t.readyResolved||(t.readyResolved=!0,t.readyResolve());break;case o.PATCH_STATE_TYPE:t.patchState(e.payload)}}),(function(e){return Boolean(e)&&"string"==typeof e.type&&e.channelName===t.channelName})),this.dispatch=this.dispatch.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this)},t=[{key:"ready",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!==e?this.readyPromise.then(e):this.readyPromise}},{key:"subscribe",value:function(e){var t=this;return this.listeners.push(e),function(){t.listeners=t.listeners.filter((function(t){return t!==e}))}}},{key:"patchState",value:function(e){this.state=this.patchStrategy(this.state,e),this.listeners.forEach((function(e){return e()}))}},{key:"replaceState",value:function(e){this.state=e,this.listeners.forEach((function(e){return e()}))}},{key:"getState",value:function(){return this.state}},{key:"replaceReducer",value:function(){}},{key:"dispatch",value:function(e){var t=this;return new Promise((function(r,i){t.serializedMessageSender({type:o.DISPATCH_TYPE,channelName:t.channelName,payload:e},null,(function(e){if(e){var o=e.error,a=e.value;if(o){var c=new Error("".concat(d).concat(o));i((0,n.default)(c,o))}else r(a&&a.payload)}else{var s=t.browserAPI.runtime.lastError,u=new Error("".concat(d).concat(s));i((0,n.default)(u,s))}}))}))}},{key:"initializeStore",value:function(e){e&&e.type===o.FETCH_STATE_TYPE&&(this.replaceState(e.payload),this.readyResolved||(this.readyResolved=!0,this.readyResolve()))}}],t&&l(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();t.default=h},3207:(e,t,r)=>{"use strict";Object.defineProperty(t,"nK",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"Iq",{enumerable:!0,get:function(){return n.default}});i(r(1732)),i(r(9449));var n=i(r(6745)),o=i(r(3988));function i(e){return e&&e.__esModule?e:{default:e}}},3807:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=Object.assign({},e);return t.forEach((function(e){var t=e.change,o=e.key,i=e.value;switch(t){case n.DIFF_STATUS_UPDATED:r[o]=i;break;case n.DIFF_STATUS_REMOVED:Reflect.deleteProperty(r,o)}})),r};var n=r(140)},3988:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=function(e){return function(){return function(t){return function(r){var n=e[r.type];return t(n?n(r):r)}}}}},6183:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getBrowserAPI=function(){var e;try{e=self.chrome||self.browser||browser}catch(t){e=browser}if(!e)throw new Error("Browser API is not present");return e}},6745:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,o=r(9529),i=r(7575),a=r(6183),c=(n=r(8642))&&n.__esModule?n:{default:n},s=r(8571);function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var l={channelName:o.DEFAULT_CHANNEL_NAME,dispatchResponder:function(e,t){Promise.resolve(e).then((function(e){t({error:null,value:e})})).catch((function(e){console.error("error dispatching result:",e),t({error:e.message,value:null})}))},serializer:i.noop,deserializer:i.noop,diffStrategy:c.default};t.default=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:l).channelName,t=void 0===e?l.channelName:e,r=(0,a.getBrowserAPI)(),n=function(e){return e.type===o.DISPATCH_TYPE&&e.channelName===t},c=(0,s.createDeferredListener)((function(e){return e.type===o.FETCH_STATE_TYPE&&e.channelName===t})),f=(0,s.createDeferredListener)(n);return r.runtime.onMessage.addListener(c.listener),r.runtime.onMessage.addListener(f.listener),function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,s=a.dispatchResponder,d=void 0===s?l.dispatchResponder:s,p=a.serializer,h=void 0===p?l.serializer:p,y=a.deserializer,g=void 0===y?l.deserializer:y,m=a.diffStrategy,v=void 0===m?l.diffStrategy:m;if("function"!=typeof h)throw new Error("serializer must be a function");if("function"!=typeof g)throw new Error("deserializer must be a function");if("function"!=typeof v)throw new Error("diffStrategy must be one of the included diffing strategies or a custom diff function");var b=(0,i.withSerializer)(h)((function(){for(var e,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];var i=function(){r.runtime.lastError};return(e=r.runtime).sendMessage.apply(e,n.concat([i])),r.tabs.query({},(function(e){var t,o=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return u(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}(e);try{for(o.s();!(t=o.n()).done;){var a,c=t.value;(a=r.tabs).sendMessage.apply(a,[c.id].concat(n,[i]))}}catch(e){o.e(e)}finally{o.f()}}))})),_=e.getState();e.subscribe((function(){var r=e.getState(),n=v(_,r);n.length&&(_=r,b({type:o.PATCH_STATE_TYPE,payload:n,channelName:t}))})),b({type:o.STATE_TYPE,payload:_,channelName:t}),c.setListener((function(t,r,n){var i=e.getState();n({type:o.FETCH_STATE_TYPE,payload:i})})),(0,i.withDeserializer)(g)(f.setListener)((function(t,r,n){var o=Object.assign({},t.payload,{_sender:r}),i=null;try{i=e.dispatch(o)}catch(e){i=Promise.reject(e.message),console.error(e)}d(i,n)}),n)}}},7575:(e,t)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,n){return(t=function(e){var t=function(e){if("object"!=r(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==r(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Object.defineProperty(t,"__esModule",{value:!0}),t.withSerializer=t.withDeserializer=t.noop=void 0;var a=t.noop=function(e){return e},c=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return o(o({},e),e.payload?{payload:t(e.payload)}:{})};t.withDeserializer=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(t){return function(r,n){return t(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=arguments.length>2?arguments[2]:void 0;return r?function(n){for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];return r.apply(void 0,[n].concat(i))?e.apply(void 0,[c(n,t)].concat(i)):e.apply(void 0,[n].concat(i))}:function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return e.apply(void 0,[c(r,t)].concat(o))}}(r,e,n))}}},t.withSerializer=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];if(o.length<=r)throw new Error("Message in request could not be serialized. "+"Expected message in position ".concat(r," but only received ").concat(o.length," args."));return o[r]=c(o[r],e),t.apply(void 0,o)}}}},8571:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createDeferredListener=void 0,t.createDeferredListener=function(e){var t=function(){},r=new Promise((function(e){return t=e}));return{setListener:t,listener:function(t,n,o){if(e(t,n,o))return r.then((function(e){e(t,n,o)})),!0}}}},8642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=[];return Object.keys(t).forEach((function(o){e[o]!==t[o]&&r.push({key:o,value:t[o],change:n.DIFF_STATUS_UPDATED})})),Object.keys(e).forEach((function(e){t.hasOwnProperty(e)||r.push({key:e,change:n.DIFF_STATUS_REMOVED})})),r};var n=r(140)},9448:function(e,t,r){!function(e){"use strict";function t(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function n(e,t){Object.defineProperty(this,"kind",{value:e,enumerable:!0}),t&&t.length&&Object.defineProperty(this,"path",{value:t,enumerable:!0})}function o(e,t,r){o.super_.call(this,"E",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0}),Object.defineProperty(this,"rhs",{value:r,enumerable:!0})}function i(e,t){i.super_.call(this,"N",e),Object.defineProperty(this,"rhs",{value:t,enumerable:!0})}function a(e,t){a.super_.call(this,"D",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0})}function c(e,t,r){c.super_.call(this,"A",e),Object.defineProperty(this,"index",{value:t,enumerable:!0}),Object.defineProperty(this,"item",{value:r,enumerable:!0})}function s(e,t,r){var n=e.slice((r||t)+1||e.length);return e.length=t<0?e.length+t:t,e.push.apply(e,n),e}function u(e){var t=void 0===e?"undefined":T(e);return"object"!==t?t:e===Math?"math":null===e?"null":Array.isArray(e)?"array":"[object Date]"===Object.prototype.toString.call(e)?"date":"function"==typeof e.toString&&/^\/.*\//.test(e.toString())?"regexp":"object"}function l(e,t,r,n,f,d,p){p=p||[];var h=(f=f||[]).slice(0);if(void 0!==d){if(n){if("function"==typeof n&&n(h,d))return;if("object"===(void 0===n?"undefined":T(n))){if(n.prefilter&&n.prefilter(h,d))return;if(n.normalize){var y=n.normalize(h,d,e,t);y&&(e=y[0],t=y[1])}}}h.push(d)}"regexp"===u(e)&&"regexp"===u(t)&&(e=e.toString(),t=t.toString());var g=void 0===e?"undefined":T(e),m=void 0===t?"undefined":T(t),v="undefined"!==g||p&&p[p.length-1].lhs&&p[p.length-1].lhs.hasOwnProperty(d),b="undefined"!==m||p&&p[p.length-1].rhs&&p[p.length-1].rhs.hasOwnProperty(d);if(!v&&b)r(new i(h,t));else if(!b&&v)r(new a(h,e));else if(u(e)!==u(t))r(new o(h,e,t));else if("date"===u(e)&&e-t!==0)r(new o(h,e,t));else if("object"===g&&null!==e&&null!==t)if(p.filter((function(t){return t.lhs===e})).length)e!==t&&r(new o(h,e,t));else{if(p.push({lhs:e,rhs:t}),Array.isArray(e)){var _;for(e.length,_=0;_<e.length;_++)_>=t.length?r(new c(h,_,new a(void 0,e[_]))):l(e[_],t[_],r,n,h,_,p);for(;_<t.length;)r(new c(h,_,new i(void 0,t[_++])))}else{var E=Object.keys(e),w=Object.keys(t);E.forEach((function(o,i){var a=w.indexOf(o);a>=0?(l(e[o],t[o],r,n,h,o,p),w=s(w,a)):l(e[o],void 0,r,n,h,o,p)})),w.forEach((function(e){l(void 0,t[e],r,n,h,e,p)}))}p.length=p.length-1}else e!==t&&("number"===g&&isNaN(e)&&isNaN(t)||r(new o(h,e,t)))}function f(e,t,r,n){return n=n||[],l(e,t,(function(e){e&&n.push(e)}),r),n.length?n:void 0}function d(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":d(o[r.path[n]],r.index,r.item);break;case"D":delete o[r.path[n]];break;case"E":case"N":o[r.path[n]]=r.rhs}}else switch(r.kind){case"A":d(e[t],r.index,r.item);break;case"D":e=s(e,t);break;case"E":case"N":e[t]=r.rhs}return e}function p(e,t,r){if(e&&t&&r&&r.kind){for(var n=e,o=-1,i=r.path?r.path.length-1:0;++o<i;)void 0===n[r.path[o]]&&(n[r.path[o]]="number"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case"A":d(r.path?n[r.path[o]]:n,r.index,r.item);break;case"D":delete n[r.path[o]];break;case"E":case"N":n[r.path[o]]=r.rhs}}}function h(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":h(o[r.path[n]],r.index,r.item);break;case"D":case"E":o[r.path[n]]=r.lhs;break;case"N":delete o[r.path[n]]}}else switch(r.kind){case"A":h(e[t],r.index,r.item);break;case"D":case"E":e[t]=r.lhs;break;case"N":e=s(e,t)}return e}function y(e){return"color: "+D[e].color+"; font-weight: bold"}function g(e,t,r,n){var o=f(e,t);try{n?r.groupCollapsed("diff"):r.group("diff")}catch(e){r.log("diff")}o?o.forEach((function(e){var t=e.kind,n=function(e){var t=e.kind,r=e.path,n=e.lhs,o=e.rhs,i=e.index,a=e.item;switch(t){case"E":return[r.join("."),n,"→",o];case"N":return[r.join("."),o];case"D":return[r.join(".")];case"A":return[r.join(".")+"["+i+"]",a];default:return[]}}(e);r.log.apply(r,["%c "+D[t].text,y(t)].concat(P(n)))})):r.log("—— no diff ——");try{r.groupEnd()}catch(e){r.log("—— diff end —— ")}}function m(e,t,r,n){switch(void 0===e?"undefined":T(e)){case"object":return"function"==typeof e[n]?e[n].apply(e,P(r)):e[n];case"function":return e(t);default:return e}}function v(e,t){var r=t.logger,n=t.actionTransformer,o=t.titleFormatter,i=void 0===o?function(e){var t=e.timestamp,r=e.duration;return function(e,n,o){var i=["action"];return i.push("%c"+String(e.type)),t&&i.push("%c@ "+n),r&&i.push("%c(in "+o.toFixed(2)+" ms)"),i.join(" ")}}(t):o,a=t.collapsed,c=t.colors,s=t.level,u=t.diff,l=void 0===t.titleFormatter;e.forEach((function(o,f){var d=o.started,p=o.startedTime,h=o.action,y=o.prevState,v=o.error,b=o.took,_=o.nextState,E=e[f+1];E&&(_=E.prevState,b=E.started-d);var w=n(h),A="function"==typeof a?a((function(){return _}),h,o):a,T=S(p),P=c.title?"color: "+c.title(w)+";":"",O=["color: gray; font-weight: lighter;"];O.push(P),t.timestamp&&O.push("color: gray; font-weight: lighter;"),t.duration&&O.push("color: gray; font-weight: lighter;");var D=i(w,T,b);try{A?c.title&&l?r.groupCollapsed.apply(r,["%c "+D].concat(O)):r.groupCollapsed(D):c.title&&l?r.group.apply(r,["%c "+D].concat(O)):r.group(D)}catch(e){r.log(D)}var j=m(s,w,[y],"prevState"),N=m(s,w,[w],"action"),x=m(s,w,[v,y],"error"),k=m(s,w,[_],"nextState");if(j)if(c.prevState){var C="color: "+c.prevState(y)+"; font-weight: bold";r[j]("%c prev state",C,y)}else r[j]("prev state",y);if(N)if(c.action){var I="color: "+c.action(w)+"; font-weight: bold";r[N]("%c action    ",I,w)}else r[N]("action    ",w);if(v&&x)if(c.error){var M="color: "+c.error(v,y)+"; font-weight: bold;";r[x]("%c error     ",M,v)}else r[x]("error     ",v);if(k)if(c.nextState){var R="color: "+c.nextState(_)+"; font-weight: bold";r[k]("%c next state",R,_)}else r[k]("next state",_);u&&g(y,_,r,A);try{r.groupEnd()}catch(e){r.log("—— log end ——")}}))}function b(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},j,e),r=t.logger,n=t.stateTransformer,o=t.errorTransformer,i=t.predicate,a=t.logErrors,c=t.diffPredicate;if(void 0===r)return function(){return function(e){return function(t){return e(t)}}};if(e.getState&&e.dispatch)return console.error("[redux-logger] redux-logger not installed. Make sure to pass logger instance as middleware:\n// Logger with default options\nimport { logger } from 'redux-logger'\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n// Or you can create your own logger with custom options http://bit.ly/redux-logger-options\nimport createLogger from 'redux-logger'\nconst logger = createLogger({\n  // ...options\n});\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n"),function(){return function(e){return function(t){return e(t)}}};var s=[];return function(e){var r=e.getState;return function(e){return function(u){if("function"==typeof i&&!i(r,u))return e(u);var l={};s.push(l),l.started=A.now(),l.startedTime=new Date,l.prevState=n(r()),l.action=u;var f=void 0;if(a)try{f=e(u)}catch(e){l.error=o(e)}else f=e(u);l.took=A.now()-l.started,l.nextState=n(r());var d=t.diff&&"function"==typeof c?c(r,u):t.diff;if(v(s,Object.assign({},t,{diff:d})),s.length=0,l.error)throw l.error;return f}}}}var _,E,w=function(e,t){return function(e,t){return new Array(t+1).join(e)}("0",t-e.toString().length)+e},S=function(e){return w(e.getHours(),2)+":"+w(e.getMinutes(),2)+":"+w(e.getSeconds(),2)+"."+w(e.getMilliseconds(),3)},A="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance:Date,T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},O=[];_="object"===(void 0===r.g?"undefined":T(r.g))&&r.g?r.g:"undefined"!=typeof window?window:{},(E=_.DeepDiff)&&O.push((function(){void 0!==E&&_.DeepDiff===f&&(_.DeepDiff=E,E=void 0)})),t(o,n),t(i,n),t(a,n),t(c,n),Object.defineProperties(f,{diff:{value:f,enumerable:!0},observableDiff:{value:l,enumerable:!0},applyDiff:{value:function(e,t,r){e&&t&&l(e,t,(function(n){r&&!r(e,t,n)||p(e,t,n)}))},enumerable:!0},applyChange:{value:p,enumerable:!0},revertChange:{value:function(e,t,r){if(e&&t&&r&&r.kind){var n,o,i=e;for(o=r.path.length-1,n=0;n<o;n++)void 0===i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case"A":h(i[r.path[n]],r.index,r.item);break;case"D":case"E":i[r.path[n]]=r.lhs;break;case"N":delete i[r.path[n]]}}},enumerable:!0},isConflict:{value:function(){return void 0!==E},enumerable:!0},noConflict:{value:function(){return O&&(O.forEach((function(e){e()})),O=null),f},enumerable:!0}});var D={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},j={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(e){return e},actionTransformer:function(e){return e},errorTransformer:function(e){return e},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},N=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dispatch,r=e.getState;return"function"==typeof t||"function"==typeof r?b()({dispatch:t,getState:r}):void console.error("\n[redux-logger v3] BREAKING CHANGE\n[redux-logger v3] Since 3.0.0 redux-logger exports by default logger with default settings.\n[redux-logger v3] Change\n[redux-logger v3] import createLogger from 'redux-logger'\n[redux-logger v3] to\n[redux-logger v3] import { createLogger } from 'redux-logger'\n")};e.defaults=j,e.createLogger=b,e.logger=N,e.default=N,Object.defineProperty(e,"__esModule",{value:!0})}(t)},9449:(e,t)=>{"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),i=1;i<t;i++)o[i-1]=arguments[i];var a,c=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},s={getState:e.getState.bind(e),dispatch:function(){return c.apply(void 0,arguments)}};return o=(o||[]).map((function(e){return e(s)})),c=n.apply(void 0,function(e){if(Array.isArray(e))return r(e)}(a=o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(a)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())(e.dispatch),e.dispatch=c,e}},9529:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.STATE_TYPE=t.PATCH_STATE_TYPE=t.FETCH_STATE_TYPE=t.DISPATCH_TYPE=t.DEFAULT_CHANNEL_NAME=void 0,t.DISPATCH_TYPE="webext.dispatch",t.FETCH_STATE_TYPE="webext.fetch_state",t.STATE_TYPE="webext.state",t.PATCH_STATE_TYPE="webext.patch_state",t.DEFAULT_CHANNEL_NAME="webext.channel"}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{"use strict";async function e(e){try{const t=await chrome.tabs.get(e);if(!t.url||t.url.startsWith("chrome://")||t.url.startsWith("chrome-extension://"))return void console.debug("Skipping content script injection for restricted URL:",t.url);console.log("injecting content script"),await chrome.scripting.executeScript({target:{tabId:e},files:["assets/vendor.js","content.js"]}).catch((e=>{console.debug("Script injection failed:",e)}))}catch(e){return void console.debug("Tab access failed:",e)}}function t(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var n=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),o=()=>Math.random().toString(36).substring(7).split("").join("."),i={INIT:`@@redux/INIT${o()}`,REPLACE:`@@redux/REPLACE${o()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${o()}`};function a(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function c(e,r,o){if("function"!=typeof e)throw new Error(t(2));if("function"==typeof r&&"function"==typeof o||"function"==typeof o&&"function"==typeof arguments[3])throw new Error(t(0));if("function"==typeof r&&void 0===o&&(o=r,r=void 0),void 0!==o){if("function"!=typeof o)throw new Error(t(1));return o(c)(e,r)}let s=e,u=r,l=new Map,f=l,d=0,p=!1;function h(){f===l&&(f=new Map,l.forEach(((e,t)=>{f.set(t,e)})))}function y(){if(p)throw new Error(t(3));return u}function g(e){if("function"!=typeof e)throw new Error(t(4));if(p)throw new Error(t(5));let r=!0;h();const n=d++;return f.set(n,e),function(){if(r){if(p)throw new Error(t(6));r=!1,h(),f.delete(n),l=null}}}function m(e){if(!a(e))throw new Error(t(7));if(void 0===e.type)throw new Error(t(8));if("string"!=typeof e.type)throw new Error(t(17));if(p)throw new Error(t(9));try{p=!0,u=s(u,e)}finally{p=!1}return(l=f).forEach((e=>{e()})),e}return m({type:i.INIT}),{dispatch:m,subscribe:g,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw new Error(t(10));s=e,m({type:i.REPLACE})},[n]:function(){const e=g;return{subscribe(r){if("object"!=typeof r||null===r)throw new Error(t(11));function n(){const e=r;e.next&&e.next(y())}return n(),{unsubscribe:e(n)}},[n](){return this}}}}}function s(e){const r=Object.keys(e),n={};for(let t=0;t<r.length;t++){const o=r[t];"function"==typeof e[o]&&(n[o]=e[o])}const o=Object.keys(n);let a;try{!function(e){Object.keys(e).forEach((r=>{const n=e[r];if(void 0===n(void 0,{type:i.INIT}))throw new Error(t(12));if(void 0===n(void 0,{type:i.PROBE_UNKNOWN_ACTION()}))throw new Error(t(13))}))}(n)}catch(e){a=e}return function(e={},r){if(a)throw a;let i=!1;const c={};for(let a=0;a<o.length;a++){const s=o[a],u=n[s],l=e[s],f=u(l,r);if(void 0===f)throw r&&r.type,new Error(t(14));c[s]=f,i=i||f!==l}return i=i||o.length!==Object.keys(e).length,i?c:e}}function u(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...r)=>e(t(...r))))}function l(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}var f=l(),d=l,p=Symbol.for("immer-nothing"),h=Symbol.for("immer-draftable"),y=Symbol.for("immer-state");function g(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var m=Object.getPrototypeOf;function v(e){return!!e&&!!e[y]}function b(e){return!!e&&(E(e)||Array.isArray(e)||!!e[h]||!!e.constructor?.[h]||P(e)||O(e))}var _=Object.prototype.constructor.toString();function E(e){if(!e||"object"!=typeof e)return!1;const t=m(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===_}function w(e,t){0===S(e)?Reflect.ownKeys(e).forEach((r=>{t(r,e[r],e)})):e.forEach(((r,n)=>t(n,r,e)))}function S(e){const t=e[y];return t?t.type_:Array.isArray(e)?1:P(e)?2:O(e)?3:0}function A(e,t){return 2===S(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function T(e,t,r){const n=S(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function P(e){return e instanceof Map}function O(e){return e instanceof Set}function D(e){return e.copy_||e.base_}function j(e,t){if(P(e))return new Map(e);if(O(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=E(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[y];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(m(e),t)}{const t=m(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function N(e,t=!1){return k(e)||v(e)||!b(e)||(S(e)>1&&(e.set=e.add=e.clear=e.delete=x),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>N(t,!0)))),e}function x(){g(2)}function k(e){return Object.isFrozen(e)}var C,I={};function M(e){const t=I[e];return t||g(0),t}function R(){return C}function F(e,t){t&&(M("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function $(e){L(e),e.drafts_.forEach(U),e.drafts_=null}function L(e){e===C&&(C=e.parent_)}function z(e){return C={drafts_:[],parent_:C,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function U(e){const t=e[y];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function B(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[y].modified_&&($(t),g(4)),b(e)&&(e=Y(t,e),t.parent_||W(t,e)),t.patches_&&M("Patches").generateReplacementPatches_(r[y].base_,e,t.patches_,t.inversePatches_)):e=Y(t,r,[]),$(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==p?e:void 0}function Y(e,t,r){if(k(t))return t;const n=t[y];if(!n)return w(t,((o,i)=>H(e,n,t,o,i,r))),t;if(n.scope_!==e)return t;if(!n.modified_)return W(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),w(o,((o,a)=>H(e,n,t,o,a,r,i))),W(e,t,!1),r&&e.patches_&&M("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function H(e,t,r,n,o,i,a){if(v(o)){const a=Y(e,o,i&&t&&3!==t.type_&&!A(t.assigned_,n)?i.concat(n):void 0);if(T(r,n,a),!v(a))return;e.canAutoFreeze_=!1}else a&&r.add(o);if(b(o)&&!k(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Y(e,o),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||W(e,o)}}function W(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&N(t,r)}var V={get(e,t){if(t===y)return e;const r=D(e);if(!A(r,t))return function(e,t,r){const n=G(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!b(n)?n:n===q(e.base_,t)?(J(e),e.copy_[t]=Q(n,e)):n},has:(e,t)=>t in D(e),ownKeys:e=>Reflect.ownKeys(D(e)),set(e,t,r){const n=G(D(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=q(D(e),t),a=n?.[y];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((o=r)===(i=n)?0!==o||1/o==1/i:o!=o&&i!=i)&&(void 0!==r||A(e.base_,t)))return!0;J(e),X(e)}var o,i;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==q(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,J(e),X(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=D(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){g(11)},getPrototypeOf:e=>m(e.base_),setPrototypeOf(){g(12)}},K={};function q(e,t){const r=e[y];return(r?D(r):e)[t]}function G(e,t){if(!(t in e))return;let r=m(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=m(r)}}function X(e){e.modified_||(e.modified_=!0,e.parent_&&X(e.parent_))}function J(e){e.copy_||(e.copy_=j(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function Q(e,t){const r=P(e)?M("MapSet").proxyMap_(e,t):O(e)?M("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:R(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=V;r&&(o=[n],i=K);const{revoke:a,proxy:c}=Proxy.revocable(o,i);return n.draft_=c,n.revoke_=a,c}(e,t);return(t?t.scope_:R()).drafts_.push(r),r}function Z(e){if(!b(e)||k(e))return e;const t=e[y];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=j(e,t.scope_.immer_.useStrictShallowCopy_)}else r=j(e,!0);return w(r,((e,t)=>{T(r,e,Z(t))})),t&&(t.finalized_=!1),r}w(V,((e,t)=>{K[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),K.deleteProperty=function(e,t){return K.set.call(this,e,t,void 0)},K.set=function(e,t,r){return V.set.call(this,e[0],t,r,e[0])};var ee=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...o){return n.produce(e,(e=>t.call(this,e,...o)))}}let n;if("function"!=typeof t&&g(6),void 0!==r&&"function"!=typeof r&&g(7),b(e)){const o=z(this),i=Q(e,void 0);let a=!0;try{n=t(i),a=!1}finally{a?$(o):L(o)}return F(o,r),B(n,o)}if(!e||"object"!=typeof e){if(n=t(e),void 0===n&&(n=e),n===p&&(n=void 0),this.autoFreeze_&&N(n,!0),r){const t=[],o=[];M("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}g(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,(t=>e(t,...r)));let r,n;return[this.produce(e,t,((e,t)=>{r=e,n=t})),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;b(e)||g(8),v(e)&&(v(t=e)||g(10),e=Z(t));const r=z(this),n=Q(e,void 0);return n[y].isManual_=!0,L(r),n}finishDraft(e,t){const r=e&&e[y];r&&r.isManual_||g(9);const{scope_:n}=r;return F(n,t),B(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=M("Patches").applyPatches_;return v(e)?n(e,t):this.produce(e,(e=>n(e,t)))}},te=ee.produce;ee.produceWithPatches.bind(ee),ee.setAutoFreeze.bind(ee),ee.setUseStrictShallowCopy.bind(ee),ee.applyPatches.bind(ee),ee.createDraft.bind(ee),ee.finishDraft.bind(ee);var re="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?u:u.apply(null,arguments)};function ne(e,t){function r(...r){if(t){let n=t(...r);if(!n)throw new Error(me(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:r[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>function(e){return a(e)&&"type"in e&&"string"==typeof e.type}(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var oe=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function ie(e){return b(e)?te(e,(()=>{})):e}function ae(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var ce=e=>t=>{setTimeout(t,e)};function se(e){const r=function(e){const{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{};let i=new oe;return t&&("boolean"==typeof t?i.push(f):i.push(d(t.extraArgument))),i},{reducer:n,middleware:o,devTools:i=!0,duplicateMiddlewareCheck:l=!0,preloadedState:p,enhancers:h}=e||{};let y,g;if("function"==typeof n)y=n;else{if(!a(n))throw new Error(me(1));y=s(n)}g="function"==typeof o?o(r):r();let m=u;i&&(m=re({trace:!1,..."object"==typeof i&&i}));const v=function(...e){return r=>(n,o)=>{const i=r(n,o);let a=()=>{throw new Error(t(15))};const c={getState:i.getState,dispatch:(e,...t)=>a(e,...t)},s=e.map((e=>e(c)));return a=u(...s)(i.dispatch),{...i,dispatch:a}}}(...g),b=(e=>function(t){const{autoBatch:r=!0}=t??{};let n=new oe(e);return r&&n.push(((e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let o=!0,i=!1,a=!1;const c=new Set,s="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:ce(10):"callback"===e.type?e.queueNotification:ce(e.timeout),u=()=>{a=!1,i&&(i=!1,c.forEach((e=>e())))};return Object.assign({},n,{subscribe(e){const t=n.subscribe((()=>o&&e()));return c.add(e),()=>{t(),c.delete(e)}},dispatch(e){try{return o=!e?.meta?.RTK_autoBatch,i=!o,i&&(a||(a=!0,s(u))),n.dispatch(e)}finally{o=!0}}})})("object"==typeof r?r:void 0)),n})(v);return c(y,p,m(..."function"==typeof h?h(b):b()))}function ue(e){const t={},r=[];let n;const o={addCase(e,r){const n="string"==typeof e?e:e.type;if(!n)throw new Error(me(28));if(n in t)throw new Error(me(29));return t[n]=r,o},addMatcher:(e,t)=>(r.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(n=e,o)};return e(o),[t,r,n]}var le=Symbol.for("rtk-slice-createasyncthunk");function fe(e,t){return`${e}/${t}`}function de({creators:e}={}){const t=e?.asyncThunk?.[le];return function(e){const{name:r,reducerPath:n=r}=e;if(!r)throw new Error(me(11));const o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},i=Object.keys(o),a={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},c={addCase(e,t){const r="string"==typeof e?e:e.type;if(!r)throw new Error(me(12));if(r in a.sliceCaseReducersByType)throw new Error(me(13));return a.sliceCaseReducersByType[r]=t,c},addMatcher:(e,t)=>(a.sliceMatchers.push({matcher:e,reducer:t}),c),exposeAction:(e,t)=>(a.actionCreators[e]=t,c),exposeCaseReducer:(e,t)=>(a.sliceCaseReducersByName[e]=t,c)};function s(){const[t={},r=[],n]="function"==typeof e.extraReducers?ue(e.extraReducers):[e.extraReducers],o={...t,...a.sliceCaseReducersByType};return function(e){let t,[i,c,s]=ue((e=>{for(let t in o)e.addCase(t,o[t]);for(let t of a.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)}));if("function"==typeof e)t=()=>ie(e());else{const r=ie(e);t=()=>r}function u(e=t(),r){let n=[i[r.type],...c.filter((({matcher:e})=>e(r))).map((({reducer:e})=>e))];return 0===n.filter((e=>!!e)).length&&(n=[s]),n.reduce(((e,t)=>{if(t){if(v(e)){const n=t(e,r);return void 0===n?e:n}if(b(e))return te(e,(e=>t(e,r)));{const n=t(e,r);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e}),e)}return u.getInitialState=t,u}(e.initialState)}i.forEach((n=>{const i=o[n],a={reducerName:n,type:fe(r,n),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(i)?function({type:e,reducerName:t,createNotation:r},n,o){let i,a;if("reducer"in n){if(r&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(n))throw new Error(me(17));i=n.reducer,a=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,a?ne(e,a):ne(e))}(a,i,c):function({type:e,reducerName:t},r,n,o){if(!o)throw new Error(me(18));const{payloadCreator:i,fulfilled:a,pending:c,rejected:s,settled:u,options:l}=r,f=o(e,i,l);n.exposeAction(t,f),a&&n.addCase(f.fulfilled,a),c&&n.addCase(f.pending,c),s&&n.addCase(f.rejected,s),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(t,{fulfilled:a||ye,pending:c||ye,rejected:s||ye,settled:u||ye})}(a,i,c,t)}));const u=e=>e,l=new Map,f=new WeakMap;let d;function p(e,t){return d||(d=s()),d(e,t)}function h(){return d||(d=s()),d.getInitialState()}function y(t,r=!1){function n(e){let o=e[t];return void 0===o&&r&&(o=ae(f,n,h)),o}function o(t=u){const n=ae(l,r,(()=>new WeakMap));return ae(n,t,(()=>{const n={};for(const[o,i]of Object.entries(e.selectors??{}))n[o]=pe(i,t,(()=>ae(f,t,h)),r);return n}))}return{reducerPath:t,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}const g={name:r,reducer:p,actions:a.actionCreators,caseReducers:a.sliceCaseReducersByName,getInitialState:h,...y(n),injectInto(e,{reducerPath:t,...r}={}){const o=t??n;return e.inject({reducerPath:o,reducer:p},r),{...g,...y(o,!0)}}};return g}}function pe(e,t,r,n){function o(o,...i){let a=t(o);return void 0===a&&n&&(a=r()),e(a,...i)}return o.unwrapped=e,o}var he=de();function ye(){}var{assign:ge}=Object;function me(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original");var ve=r(9448),be=r(3207);const _e={isLoaded:!1},Ee=he({name:"content",initialState:_e,reducers:{reset:()=>_e,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:we,reducer:Se}=Ee;var Ae,Te;!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(Ae||(Ae={})),function(e){e.Default="default"}(Te||(Te={}));const Pe={isOpen:!1},Oe=he({name:"sidePanel",initialState:Pe,reducers:{reset:()=>Pe}}),{actions:De,reducer:je}=Oe,Ne={},xe=((0,be.nK)(Ne),ve.logger,(0,be.nK)(Ne),ve.logger,[(0,be.nK)(Ne),ve.logger]);var ke;!function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(ke||(ke={}));const Ce=ke;(({channelName:e}={})=>{const t=se({devTools:!0,reducer:s({sidePanel:je,content:Se}),middleware:e=>e().concat(xe)});e&&(0,be.Iq)({channelName:e})(t)})({channelName:Ce.ContentPort}),chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:!0}).catch(console.error);const Ie=new Map,Me="chromepanionAddToNoteSelection",Re="chromepanionAddPageToNoteSystem",Fe=new Set;chrome.contextMenus.create({id:Me,title:"Add to ChromePanion Note",contexts:["selection"],enabled:!1},(()=>{chrome.runtime.lastError&&(["duplicate id "+Me,"item already exists"].some((e=>chrome.runtime.lastError?.message?.includes(e)))||console.warn(`Initial attempt to create context menu '${Me}' encountered an issue: ${chrome.runtime.lastError.message}`))})),chrome.contextMenus.create({id:Re,title:"Add Page to ChromePanion Note System",contexts:["page"],enabled:!0},(()=>{chrome.runtime.lastError&&(["duplicate id "+Re,"item already exists"].some((e=>chrome.runtime.lastError?.message?.includes(e)))||console.warn(`Initial attempt to create context menu '${Re}' encountered an issue: ${chrome.runtime.lastError.message}`))})),chrome.runtime.onInstalled.addListener((()=>{chrome.contextMenus.remove(Me,(()=>{!chrome.runtime.lastError||chrome.runtime.lastError.message?.includes("No such context menu")||chrome.runtime.lastError.message?.includes("Cannot find menu item")||console.warn(`Error removing context menu '${Me}' during onInstalled: ${chrome.runtime.lastError.message}`),chrome.contextMenus.create({id:Me,title:"Add to ChromePanion Note",contexts:["selection"],enabled:!1},(()=>{chrome.runtime.lastError&&console.error(`Error creating/recreating context menu '${Me}' in onInstalled: ${chrome.runtime.lastError.message}`)}))})),chrome.contextMenus.remove(Re,(()=>{!chrome.runtime.lastError||chrome.runtime.lastError.message?.includes("No such context menu")||chrome.runtime.lastError.message?.includes("Cannot find menu item")||console.warn(`Error removing context menu '${Re}' during onInstalled: ${chrome.runtime.lastError.message}`),chrome.contextMenus.create({id:Re,title:"Add Page to ChromePanion Note System",contexts:["page"],enabled:!0},(()=>{chrome.runtime.lastError&&console.error(`Error creating/recreating context menu '${Re}' in onInstalled: ${chrome.runtime.lastError.message}`)}))}))})),chrome.contextMenus.onClicked.addListener(((e,t)=>{if(e.menuItemId===Me&&$e)e.selectionText?$e.postMessage({type:"ADD_SELECTION_TO_NOTE",payload:e.selectionText.trim()}):console.warn("Context menu 'Add to ChromePanion Note' clicked, but no selectionText found.");else if(e.menuItemId===Re&&t?.id){const e=t.id;if(Fe.has(e))return void console.warn(`[Background] Action ${Re} is already in progress for tab ${e}. Ignoring duplicate request.`);Fe.add(e),console.log(`[Background] Started processing ${Re} for tab ${e}.`),(async()=>{try{console.log(`[Background] Action: ${Re}. Attempting to open side panel for tab ${e}.`),await chrome.sidePanel.open({tabId:e}),console.log(`[Background] Side panel open command issued for tab ${e}.`);const t="content.js";let r=!1;try{console.log(`[Background] Ensuring content script ('${t}') is active in tab ${e}.`),await chrome.scripting.executeScript({target:{tabId:e},files:[t]}),console.log(`[Background] Content script injection/execution of '${t}' ensured for tab ${e}.`),r=!0}catch(n){if(!n.message?.includes("already injected"))return console.error(`[Background] Failed to inject content script '${t}' into tab ${e}:`,n),Ie.set(e,{title:"Error",content:"Failed to prepare page for content extraction."}),Fe.delete(e),void console.log(`[Background] Cleared processing flag for tab ${e} due to content script injection error.`);console.warn(`[Background] Content script already present in tab ${e}.`),r=!0}if(!r)return console.error(`[Background] Content script was not ensured for tab ${e}. Aborting.`),void Fe.delete(e);console.log(`[Background] Sending DEFUDDLE_PAGE_CONTENT to content script in tab ${e}.`),((e,t,r=4,n=300)=>new Promise(((o,i)=>{let a=0;const c=()=>{a++,chrome.tabs.sendMessage(e,t,(t=>{if(chrome.runtime.lastError){const t=chrome.runtime.lastError.message||"Unknown error";a<r&&(t.includes("Could not establish connection")||t.includes("Receiving end does not exist"))?(console.warn(`[Background] Attempt ${a}/${r} to send message to content script in tab ${e} failed: ${t}. Retrying in ${n}ms...`),setTimeout(c,n)):(console.error(`[Background] Failed to send message to content script in tab ${e} after ${a} attempts: ${t}`),i(chrome.runtime.lastError))}else o(t)}))};c()})))(e,{type:"DEFUDDLE_PAGE_CONTENT"}).then((t=>{if(t&&t.success)console.log(`[Background] Received defuddled content for tab ${e}. Storing it.`),Ie.set(e,{title:t.title,content:t.content});else{console.warn(`[Background] Failed to defuddle page content for tab ${e}. Response:`,t);const r=t?.error||"Unknown error processing page content.";Ie.set(e,{title:"Error",content:r})}})).catch((t=>{console.error(`[Background] Error sending/receiving DEFUDDLE_PAGE_CONTENT for tab ${e} after retries:`,t.message),Ie.set(e,{title:"Error",content:`Failed to get page content: ${t.message}. The page might be protected or need a reload.`})})).finally((()=>{console.log(`[Background] Requesting side panel to switch to Note System View for tab ${e}.`),chrome.runtime.sendMessage({type:"ACTIVATE_NOTE_SYSTEM_VIEW",payload:{tabId:e}},(e=>{chrome.runtime.lastError?console.error(`[Background] Error sending ACTIVATE_NOTE_SYSTEM_VIEW to side panel: ${chrome.runtime.lastError.message}`):console.log("[Background] Side panel acknowledged ACTIVATE_NOTE_SYSTEM_VIEW. Response:",e)})),Fe.delete(e),console.log(`[Background] Cleared processing flag for tab ${e} after DEFUDDLE_PAGE_CONTENT attempt.`)}))}catch(t){console.error(`[Background] Error during '${Re}' main execution for tab ${e}:`,t.message,t.stack),chrome.notifications&&chrome.notifications.create("cognitoMainActionError_"+Date.now(),{type:"basic",iconUrl:chrome.runtime.getURL("icons/icon48.png"),title:"Cognito Action Error",message:`Could not process page: ${t.message}. Try reloading the page or the extension.`}),Fe.delete(e),console.log(`[Background] Cleared processing flag for tab ${e} due to main execution error.`)}})()}}));let $e=null;chrome.runtime.onConnect.addListener((t=>{if(t.name===Ce.SidePanelPort){$e=t,setTimeout((()=>{chrome.contextMenus.update(Me,{enabled:!0},(()=>{chrome.runtime.lastError&&console.error("Error enabling context menu (after timeout):",chrome.runtime.lastError.message)}))}),0);let r=!1;const n=async t=>{const r=await chrome.tabs.get(t.tabId);r?.url&&!r.url.startsWith("chrome")&&e(t.tabId)},o=async(t,r,n)=>{n?.url&&"complete"===r.status&&!n.url.startsWith("chrome")&&e(t)};t.onMessage.addListener((async t=>{if("init"===t.type){const t=await async function(){const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});return e}();t?.id&&t.url&&!t.url.startsWith("chrome")&&e(t.id),r||(chrome.tabs.onActivated.addListener(n),chrome.tabs.onUpdated.addListener(o),r=!0)}})),t.onDisconnect.addListener((()=>{chrome.contextMenus.update(Me,{enabled:!1},(()=>{chrome.runtime.lastError&&console.warn("Error disabling context menu:",chrome.runtime.lastError.message)})),$e===t&&($e=null),r&&(chrome.tabs.onActivated.removeListener(n),chrome.tabs.onUpdated.removeListener(o),r=!1)}))}})),chrome.runtime.onMessage.addListener(((e,t,r)=>{if("SIDE_PANEL_READY"===e.type){const t=e.tabId;if(!t)return console.error("[Background] SIDE_PANEL_READY received, but tabId is missing in the message payload."),r({status:"error",message:"Missing tabId"}),!1;if(console.log(`[Background] SIDE_PANEL_READY received for tab ${t}.`),Ie.has(t)){const e=Ie.get(t),n="Error"===e.title?"ERROR_OCCURRED":"CREATE_NOTE_FROM_PAGE_CONTENT",o="Error"===e.title?e.content:e;console.log(`[Background] Found pending payload for tab ${t}. Sending ${n} to the extension runtime.`),chrome.runtime.sendMessage({type:n,payload:o},(e=>{chrome.runtime.lastError?console.error(`[Background] Error sending ${n} to runtime. Side panel might not be ready. Error:`,chrome.runtime.lastError.message):console.log(`[Background] Side panel acknowledged receipt of ${n}. Response:`,e)})),Ie.delete(t),r({status:"Payload delivery initiated via runtime message."})}else console.log(`[Background] SIDE_PANEL_READY received for tab ${t}, but no pending payload was found. This is normal if the user opened the panel manually.`),r({status:"Ready signal received, no pending action."});return!0}if("SAVE_NOTE_TO_FILE"===e.type&&e.payload){const{content:t}=e.payload;if(t){const e=`cognito_note_${(new Date).toISOString().replace(/[:.]/g,"-")}.md`,n=`data:text/markdown;charset=utf-8,${encodeURIComponent(t)}`;chrome.downloads.download({url:n,filename:e,saveAs:!0},(e=>{chrome.runtime.lastError?(console.error("Download failed:",chrome.runtime.lastError),r({success:!1,error:chrome.runtime.lastError.message})):e?(console.log("Download started with ID:",e),r({success:!0,downloadId:e})):(console.warn("Download did not start, downloadId is undefined."),r({success:!1,error:"Download did not start (downloadId undefined)."}))}))}else r({success:!1,error:"No content provided for saving note."});return!0}return!0}))})()})();