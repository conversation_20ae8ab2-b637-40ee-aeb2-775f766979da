import { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { FiRefreshCw, FiAlertCircle, FiCheck } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useConfig } from './ConfigContext';
import { useUpdateModels } from './hooks/useUpdateModels';
import { cn } from "@/src/background/util";
import type { Model } from 'src/types/config';

export const ModelSelector = () => {
  const { config, updateConfig } = useConfig();
  const { fetchAllModels } = useUpdateModels();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleRefreshModels = async () => {
    if (!config?.ollamaUrl || !config?.ollamaConnected) {
      setError('Ollama not connected');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await fetchAllModels();
      toast.success('Models refreshed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh models';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Error refreshing models:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fetch models when Ollama connection changes
  useEffect(() => {
    if (config?.ollamaConnected) {
      handleRefreshModels();
    } else {
      setError(null);
    }
  }, [config?.ollamaConnected, config?.ollamaUrl]);

  const handleModelSelect = (modelId: string) => {
    updateConfig({ selectedModel: modelId });
    toast.success(`Selected model: ${modelId}`);
  };

  const isConnected = config?.ollamaConnected;
  const selectedModel = config?.selectedModel;
  const availableModels = config?.models?.filter(m => m.host === 'ollama') || [];

  if (!isConnected) {
    return (
      <div className="flex items-center justify-center py-4 text-[var(--text)]/60">
        <FiAlertCircle className="mr-2 h-4 w-4" />
        <span className="text-sm">Connect to Ollama first</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center text-red-500">
            <FiAlertCircle className="mr-2 h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
          <Button
            onClick={handleRefreshModels}
            variant="ghost"
            size="sm"
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            <FiRefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </div>
    );
  }

  if (availableModels.length === 0 && !isLoading) {
    return (
      <div className="flex items-center justify-between py-2">
        <div className="flex items-center text-[var(--text)]/60">
          <FiAlertCircle className="mr-2 h-4 w-4" />
          <span className="text-sm">No models available</span>
        </div>
        <Button
          onClick={handleRefreshModels}
          variant="ghost"
          size="sm"
          disabled={isLoading}
          className="h-8 w-8 p-0"
        >
          <FiRefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      <div className="flex-1">
        <Select
          value={selectedModel || ''}
          onValueChange={handleModelSelect}
          disabled={isLoading || availableModels.length === 0}
        >
          <SelectTrigger
            variant="settingsPanel"
            className={cn(
              "w-full",
              isLoading && "opacity-50"
            )}
          >
            <SelectValue
              placeholder={isLoading ? "Loading models..." : "Select a model..."}
            />
          </SelectTrigger>
          <SelectContent variant="settingsPanel">
            {availableModels.map((model) => (
              <SelectItem
                key={model.id}
                value={model.id}
                focusVariant="activeTheme"
                className="text-[var(--text)]"
              >
                <div className="flex flex-col">
                  <span className="font-medium">{model.name || model.id}</span>
                  {model.context_length && (
                    <span className="text-xs text-[var(--text)]/60">
                      Context: {model.context_length}
                    </span>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center space-x-2">
        {selectedModel && availableModels.some(m => m.id === selectedModel) && (
          <div className="flex items-center text-[var(--success)]">
            <FiCheck className="h-4 w-4" />
          </div>
        )}

        <Button
          onClick={handleRefreshModels}
          variant="ghost"
          size="sm"
          disabled={isLoading}
          className="h-8 w-8 p-0 hover:bg-[var(--text)]/10"
          title="Refresh models"
        >
          <FiRefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
        </Button>
      </div>
    </div>
  );
};
